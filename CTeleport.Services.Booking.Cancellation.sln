
Microsoft Visual Studio Solution File, Format Version 12.00
#
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CTeleport.Services.Booking.Cancellation.API", "src\CTeleport.Services.Booking.Cancellation.API\CTeleport.Services.Booking.Cancellation.API.csproj", "{9D58ECB3-6CC8-47A3-B2F4-0084866338E4}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{03855BE9-1DF9-4BBD-B3FE-3FB5355DEFDF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CTeleport.Services.Booking.Cancellation.Client", "src\CTeleport.Services.Booking.Cancellation.Client\CTeleport.Services.Booking.Cancellation.Client.csproj", "{7032F87C-6E56-409E-9522-AB7E2164A666}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CTeleport.Services.Booking.Cancellation.Models", "src\CTeleport.Services.Booking.Cancellation.Models\CTeleport.Services.Booking.Cancellation.Models.csproj", "{E5A17D7F-E735-4EDE-9C37-DF6C80786332}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CTeleport.Services.Booking.Cancellation.Messages", "src\CTeleport.Services.Booking.Cancellation.Messages\CTeleport.Services.Booking.Cancellation.Messages.csproj", "{2B2CA351-FA48-48B0-924E-EDEFE6FE5FA2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CTeleport.Services.Booking.Cancellation.Service", "src\CTeleport.Services.Booking.Cancellation.Service\CTeleport.Services.Booking.Cancellation.Service.csproj", "{54FE7642-8D4E-4251-9E16-E6005BFBD69E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{E94EC175-1260-4C29-8021-B5970D3D80EC}"
	ProjectSection(SolutionItems) = preProject
		.gitignore = .gitignore
		.gitlab-ci.yml = .gitlab-ci.yml
		Directory.Packages.props = Directory.Packages.props
		docker-compose.yml = docker-compose.yml
		NuGet.Config = NuGet.Config
		Dockerfile = Dockerfile
		README.md = README.md
		.editorconfig = .editorconfig
		setup.sh = setup.sh
		.config\dotnet-tools.json = .config\dotnet-tools.json
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CTeleport.Services.Booking.Cancellation.Messaging", "src\CTeleport.Services.Booking.Cancellation.Messaging\CTeleport.Services.Booking.Cancellation.Messaging.csproj", "{B3B59407-4322-4B9C-911D-BF49DC8E5CBA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CTeleport.Services.Booking.Cancellation.API.Integration", "tests\CTeleport.Services.Booking.Cancellation.API.Integration\CTeleport.Services.Booking.Cancellation.API.Integration.csproj", "{C30644EC-8128-4391-B8F0-DE273B36BACA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CTeleport.Services.Booking.Cancellation.Service.Unit", "tests\CTeleport.Services.Booking.Cancellation.Service.Unit\CTeleport.Services.Booking.Cancellation.Service.Unit.csproj", "{0818249F-34BC-4EC9-BE41-03BF707CFB17}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CTeleport.Services.Booking.Cancellation.Architecture", "tests\CTeleport.Services.Booking.Cancellation.Architecture\CTeleport.Services.Booking.Cancellation.Architecture.csproj", "{C7013D88-DF1F-4A7D-A9DE-70CFBDE5DEC2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CTeleport.Services.Booking.Cancellation.RefundQueue", "src\CTeleport.Services.Booking.Cancellation.RefundQueue\CTeleport.Services.Booking.Cancellation.RefundQueue.csproj", "{B7C1C4B6-EEEF-494C-8DDD-D550D1AF1CE7}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{9D58ECB3-6CC8-47A3-B2F4-0084866338E4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9D58ECB3-6CC8-47A3-B2F4-0084866338E4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9D58ECB3-6CC8-47A3-B2F4-0084866338E4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9D58ECB3-6CC8-47A3-B2F4-0084866338E4}.Release|Any CPU.Build.0 = Release|Any CPU
		{7032F87C-6E56-409E-9522-AB7E2164A666}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7032F87C-6E56-409E-9522-AB7E2164A666}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7032F87C-6E56-409E-9522-AB7E2164A666}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7032F87C-6E56-409E-9522-AB7E2164A666}.Release|Any CPU.Build.0 = Release|Any CPU
		{E5A17D7F-E735-4EDE-9C37-DF6C80786332}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E5A17D7F-E735-4EDE-9C37-DF6C80786332}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E5A17D7F-E735-4EDE-9C37-DF6C80786332}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E5A17D7F-E735-4EDE-9C37-DF6C80786332}.Release|Any CPU.Build.0 = Release|Any CPU
		{2B2CA351-FA48-48B0-924E-EDEFE6FE5FA2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2B2CA351-FA48-48B0-924E-EDEFE6FE5FA2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2B2CA351-FA48-48B0-924E-EDEFE6FE5FA2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2B2CA351-FA48-48B0-924E-EDEFE6FE5FA2}.Release|Any CPU.Build.0 = Release|Any CPU
		{54FE7642-8D4E-4251-9E16-E6005BFBD69E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{54FE7642-8D4E-4251-9E16-E6005BFBD69E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{54FE7642-8D4E-4251-9E16-E6005BFBD69E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{54FE7642-8D4E-4251-9E16-E6005BFBD69E}.Release|Any CPU.Build.0 = Release|Any CPU
		{B3B59407-4322-4B9C-911D-BF49DC8E5CBA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B3B59407-4322-4B9C-911D-BF49DC8E5CBA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B3B59407-4322-4B9C-911D-BF49DC8E5CBA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B3B59407-4322-4B9C-911D-BF49DC8E5CBA}.Release|Any CPU.Build.0 = Release|Any CPU
		{C30644EC-8128-4391-B8F0-DE273B36BACA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C30644EC-8128-4391-B8F0-DE273B36BACA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C30644EC-8128-4391-B8F0-DE273B36BACA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C30644EC-8128-4391-B8F0-DE273B36BACA}.Release|Any CPU.Build.0 = Release|Any CPU
		{0818249F-34BC-4EC9-BE41-03BF707CFB17}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0818249F-34BC-4EC9-BE41-03BF707CFB17}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0818249F-34BC-4EC9-BE41-03BF707CFB17}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0818249F-34BC-4EC9-BE41-03BF707CFB17}.Release|Any CPU.Build.0 = Release|Any CPU
		{C7013D88-DF1F-4A7D-A9DE-70CFBDE5DEC2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C7013D88-DF1F-4A7D-A9DE-70CFBDE5DEC2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C7013D88-DF1F-4A7D-A9DE-70CFBDE5DEC2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C7013D88-DF1F-4A7D-A9DE-70CFBDE5DEC2}.Release|Any CPU.Build.0 = Release|Any CPU
		{B7C1C4B6-EEEF-494C-8DDD-D550D1AF1CE7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B7C1C4B6-EEEF-494C-8DDD-D550D1AF1CE7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B7C1C4B6-EEEF-494C-8DDD-D550D1AF1CE7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B7C1C4B6-EEEF-494C-8DDD-D550D1AF1CE7}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{C30644EC-8128-4391-B8F0-DE273B36BACA} = {03855BE9-1DF9-4BBD-B3FE-3FB5355DEFDF}
		{0818249F-34BC-4EC9-BE41-03BF707CFB17} = {03855BE9-1DF9-4BBD-B3FE-3FB5355DEFDF}
		{C7013D88-DF1F-4A7D-A9DE-70CFBDE5DEC2} = {03855BE9-1DF9-4BBD-B3FE-3FB5355DEFDF}
	EndGlobalSection
EndGlobal
