using System.Text.Json.Serialization;
using CTeleport.Common.Messaging.CAP;
using CTeleport.Common.Mongo;
using MongoDB.Driver;

namespace CTeleport.Services.Booking.Cancellation.API.Integration;

public abstract class BaseIntegrationTest(ApiFactory factory)
{
    protected IFixture Fixture { get; } = new Fixture();

    protected static JsonSerializerOptions SerializerOptions =>
        new() { PropertyNameCaseInsensitive = true, Converters = { new JsonStringEnumConverter() } };

    protected Task SeedDatabase(params string[] trips) => GetDatabase().GetCollection<string>("[TODO]").InsertManyAsync(trips);

    private IMongoDatabase GetDatabase()
    {
        var serviceProvider = factory.Services;
        var configuration = serviceProvider.GetRequiredService<IConfiguration>();
        var mongoDbSettings = configuration.GetSettings<MongoDbSettings>();

        var client = new MongoClient(mongoDbSettings.ConnectionString);
        return client.GetDatabase(mongoDbSettings.Database);
    }
}
