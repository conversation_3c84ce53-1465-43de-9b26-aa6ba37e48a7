using Microsoft.AspNetCore.Hosting;
using Testcontainers.MongoDb;
using Testcontainers.RabbitMq;
using Testcontainers.Redis;

namespace CTeleport.Services.Booking.Cancellation.API.Integration;

public class ApiFactory : WebApplicationFactory<Program>, IAsyncLifetime
{
    private const int MongoDbPort = 27017;
    private const int RabbitMqPort = 5672;
    private const int RabbitMqApiPort = 15672;
    private const int RedisPort = 6379;

    private const string MongoDbUsername = "mongo";
    private const string MongoDbPassword = "mongo";
    private const string RabbitMqUsername = "guest";
    private const string RabbitMqPassword = "guest";

    private readonly MongoDbContainer _mongoDbContainer = new MongoDbBuilder()
        .WithUsername(MongoDbUsername)
        .WithPassword(MongoDbPassword)
        .WithReplicaSet()
        .WithPortBinding(MongoDbPort, true)
        .Build();

    private readonly RabbitMqContainer _rabbitMqContainer = new RabbitMqBuilder()
        .WithImage("rabbitmq:4-management")
        .WithUsername(RabbitMqUsername)
        .WithPassword(RabbitMqPassword)
        .WithPortBinding(RabbitMqPort, true)
        .WithPortBinding(RabbitMqApiPort, true)
        .Build();

    private readonly RedisContainer _redisContainer = new RedisBuilder().WithPortBinding(RedisPort, true).Build();

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureAppConfiguration(config => config.AddJsonFile("appsettings.json").AddEnvironmentVariables());

        var mongoPort = _mongoDbContainer.GetMappedPublicPort(MongoDbPort);
        var rabbitPort = _rabbitMqContainer.GetMappedPublicPort(RabbitMqPort);
        var rabbitApiPort = _rabbitMqContainer.GetMappedPublicPort(RabbitMqApiPort);

        Environment.SetEnvironmentVariable("MongoDb:ConnectionString", GetMongoDbConnectionString(mongoPort));
        Environment.SetEnvironmentVariable("RabbitMq:Username", RabbitMqUsername);
        Environment.SetEnvironmentVariable("RabbitMq:Password", RabbitMqPassword);
        Environment.SetEnvironmentVariable("RabbitMq:Port", rabbitPort.ToString());
        Environment.SetEnvironmentVariable("RabbitMq:ApiPort", rabbitApiPort.ToString());
        Environment.SetEnvironmentVariable("Redis:ConnectionString", _redisContainer.GetConnectionString());
    }

    public Task InitializeAsync() => Task.WhenAll(_mongoDbContainer.StartAsync(), _rabbitMqContainer.StartAsync(), _redisContainer.StartAsync());

    public new Task DisposeAsync() =>
        Task.WhenAll(_mongoDbContainer.DisposeAsync().AsTask(), _rabbitMqContainer.DisposeAsync().AsTask(), _redisContainer.DisposeAsync().AsTask());

    private static string GetMongoDbConnectionString(ushort port) =>
        $"mongodb://{MongoDbUsername}:{MongoDbPassword}@localhost:{port}/?directConnection=true";
}
