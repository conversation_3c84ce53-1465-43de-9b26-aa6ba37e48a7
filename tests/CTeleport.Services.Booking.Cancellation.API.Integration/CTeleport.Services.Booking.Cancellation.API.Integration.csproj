<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <InvariantGlobalization>true</InvariantGlobalization>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\CTeleport.Services.Booking.Cancellation.API\CTeleport.Services.Booking.Cancellation.API.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoFixture"/>
    <PackageReference Include="coverlet.collector"/>
    <PackageReference Include="FluentAssertions"/>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing"/>
    <PackageReference Include="Microsoft.NET.Test.Sdk"/>
    <PackageReference Include="NSubstitute"/>
    <PackageReference Include="SonarAnalyzer.CSharp">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Testcontainers" />
    <PackageReference Include="Testcontainers.MongoDb" />
    <PackageReference Include="Testcontainers.RabbitMq" />
    <PackageReference Include="Testcontainers.Redis" />
    <PackageReference Include="xunit"/>
    <PackageReference Include="xunit.runner.visualstudio"/>
  </ItemGroup>

  <ItemGroup>
    <Using Include="AutoFixture"/>
    <Using Include="FluentAssertions"/>
    <Using Include="Microsoft.AspNetCore.Mvc"/>
    <Using Include="Microsoft.AspNetCore.Mvc.Testing"/>
    <Using Include="Microsoft.Extensions.Configuration"/>
    <Using Include="Microsoft.Extensions.DependencyInjection"/>
    <Using Include="System.Net"/>
    <Using Include="System.Net.Http.Json"/>
    <Using Include="System.Text.Json"/>
    <Using Include="Xunit"/>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Apis\" />
  </ItemGroup>

</Project>
