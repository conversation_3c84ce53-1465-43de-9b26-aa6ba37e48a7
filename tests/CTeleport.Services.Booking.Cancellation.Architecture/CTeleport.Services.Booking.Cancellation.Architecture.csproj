<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>

        <IsPackable>false</IsPackable>
        <IsTestProject>true</IsTestProject>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="coverlet.collector" Version="6.0.0"/>
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0"/>
        <PackageReference Include="NetArchTest.Rules" Version="1.3.2" />
        <PackageReference Include="Shouldly" Version="4.3.0" />
        <PackageReference Include="xunit" Version="2.5.3"/>
        <PackageReference Include="xunit.runner.visualstudio" Version="2.5.3"/>
    </ItemGroup>

    <ItemGroup>
        <Using Include="Xunit"/>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\src\CTeleport.Services.Booking.Cancellation.API\CTeleport.Services.Booking.Cancellation.API.csproj" />
      <ProjectReference Include="..\..\src\CTeleport.Services.Booking.Cancellation.Client\CTeleport.Services.Booking.Cancellation.Client.csproj" />
      <ProjectReference Include="..\..\src\CTeleport.Services.Booking.Cancellation.Messaging\CTeleport.Services.Booking.Cancellation.Messaging.csproj" />
      <ProjectReference Include="..\..\src\CTeleport.Services.Booking.Cancellation.Models\CTeleport.Services.Booking.Cancellation.Models.csproj" />
      <ProjectReference Include="..\..\src\CTeleport.Services.Booking.Cancellation.Service\CTeleport.Services.Booking.Cancellation.Service.csproj" />
    </ItemGroup>

</Project>
