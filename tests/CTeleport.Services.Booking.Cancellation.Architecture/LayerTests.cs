using NetArchTest.Rules;
using Shouldly;

namespace CTeleport.Services.Booking.Cancellation.Architecture;

public class LayerTests : BaseArchitectureTest
{
    [Fact]
    public void Service_ExpectNoDependencyOnApi()
    {
        var actual = Types.InAssembly(ServiceAssembly).Should().NotHaveDependencyOn(ApiAssembly.GetName().Name).GetResult();

        actual.IsSuccessful.ShouldBeTrue();
    }

    [Fact]
    public void Service_ExpectNoDependencyOnClient()
    {
        var actual = Types.InAssembly(ServiceAssembly).Should().NotHaveDependencyOn(ClientAssembly.GetName().Name).GetResult();

        actual.IsSuccessful.ShouldBeTrue();
    }

    [Fact]
    public void Service_ExpectNoDependencyOnMessaging()
    {
        var actual = Types.InAssembly(ServiceAssembly).Should().NotHaveDependencyOn(MessagingAssembly.GetName().Name).GetResult();

        actual.IsSuccessful.ShouldBeTrue();
    }
}
