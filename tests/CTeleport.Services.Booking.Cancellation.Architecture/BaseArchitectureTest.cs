using System.Reflection;

namespace CTeleport.Services.Booking.Cancellation.Architecture;

public abstract class BaseArchitectureTest
{
    protected static readonly Assembly ApiAssembly = typeof(API.Program).Assembly;
    protected static readonly Assembly ClientAssembly = typeof(Client.Policies).Assembly;
    protected static readonly Assembly MessagingAssembly = typeof(Messaging.Utils.ServiceCollectionExtensions).Assembly;
    protected static readonly Assembly ServiceAssembly = typeof(Service.Utils.ServiceCollectionExtensions).Assembly;
}
