using CTeleport.Common.Messaging;
using CTeleport.Common.Messaging.CAP.MongoDB.Transactions;
using CTeleport.Services.Booking.Cancellation.Messages.Events;
using CTeleport.Services.Booking.Cancellation.Service.Constants;
using CTeleport.Services.Booking.Cancellation.Service.Models;
using CTeleport.Services.Booking.Cancellation.Service.Models.Requests;
using CTeleport.Services.Booking.Cancellation.Service.Repositories;
using CTeleport.Services.Booking.Cancellation.Service.Services;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using NSubstitute;

namespace CTeleport.Services.Booking.Cancellation.Service.Unit.Services;

public class TrainReservationServiceTests
{
    private readonly Fixture _fixture = new();

    private readonly ITrainCancellationRepository _trainCancellationRepository = Substitute.For<ITrainCancellationRepository>();
    private readonly ITransactionScope _scope = Substitute.For<ITransactionScope>();
    private readonly IMessageDispatcher _dispatcher = Substitute.For<IMessageDispatcher>();
    private readonly IClientSessionHandle _session = Substitute.For<IClientSessionHandle>();

    private readonly TimeProvider _timeProvider = Substitute.For<TimeProvider>();
    private readonly ILogger<TrainReservationService> _logger = Substitute.For<ILogger<TrainReservationService>>();

    [Fact]
    public async Task HandleSetTrainReservationCancelledAsync_ExpectFalseAndDontDispatchEvents_WhenBookingIsNotFound()
    {
        // Arrange
        const string bookingId = "booking_id";
        const string reservationId = "reservation_id";

        var sut = new TrainReservationService(_trainCancellationRepository, _scope, _dispatcher, _timeProvider, _logger);

        var request = _fixture
            .Build<SetTrainReservationCancelledRequest>()
            .With(i => i.BookingId, bookingId)
            .With(i => i.ReservationId, reservationId)
            .Create();

        _timeProvider.GetUtcNow().Returns(DateTimeOffset.UtcNow);
        _trainCancellationRepository
            .GetByIdAsync(Arg.Any<IClientSessionHandle>(), request.BookingId, Arg.Any<CancellationToken>())
            .Returns(null as TrainBooking);

        _trainCancellationRepository
            .CancelTrainReservationAsync(
                Arg.Any<IClientSessionHandle>(),
                request.BookingId,
                Arg.Any<TrainReservation>(),
                Arg.Any<CancellationToken>()
            )
            .Returns(true);

        // Act
        var result = await sut.HandleSetTrainReservationCancelledAsync(_session, request, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.ErrorMessage.Should().Be($"There is no train booking record for booking with BookingId: {request.BookingId}");

        await _trainCancellationRepository.Received(1).GetByIdAsync(Arg.Any<IClientSessionHandle>(), request.BookingId, Arg.Any<CancellationToken>());
        await _trainCancellationRepository
            .DidNotReceive()
            .CancelTrainReservationAsync(
                Arg.Any<IClientSessionHandle>(),
                request.BookingId,
                Arg.Any<TrainReservation>(),
                Arg.Any<CancellationToken>()
            );
        _timeProvider.DidNotReceive().GetUtcNow();
        await _dispatcher.DidNotReceive().DispatchAsync(Arg.Any<BookingTrainReservationCancelledEvent>(), Arg.Any<string>());
        await _dispatcher.DidNotReceive().DispatchAsync(Arg.Any<BookingCancelledEvent>(), Arg.Any<string>());
    }

    [Fact]
    public async Task HandleSetTrainReservationCancelledAsync_ExpectFalseAndDontDispatchEvents_WhenReservationIsNotFoundInBooking()
    {
        // Arrange
        const string bookingId = "booking_id";
        const string reservationId = "reservation_id";

        var sut = new TrainReservationService(_trainCancellationRepository, _scope, _dispatcher, _timeProvider, _logger);

        var request = _fixture
            .Build<SetTrainReservationCancelledRequest>()
            .With(i => i.BookingId, bookingId)
            .With(i => i.ReservationId, reservationId)
            .Create();

        var booking = _fixture.Build<TrainBooking>().With(i => i.BookingId, bookingId).Create();
        booking.Reservations = [];

        _timeProvider.GetUtcNow().Returns(DateTimeOffset.UtcNow);
        _trainCancellationRepository.GetByIdAsync(Arg.Any<IClientSessionHandle>(), request.BookingId, Arg.Any<CancellationToken>()).Returns(booking);

        _trainCancellationRepository
            .CancelTrainReservationAsync(
                Arg.Any<IClientSessionHandle>(),
                request.BookingId,
                Arg.Any<TrainReservation>(),
                Arg.Any<CancellationToken>()
            )
            .Returns(true);

        // Act
        var result = await sut.HandleSetTrainReservationCancelledAsync(_session, request, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result
            .ErrorMessage.Should()
            .Be($"There is no train reservation record id {request.ReservationId} for booking with BookingId: {request.BookingId}");

        await _trainCancellationRepository.Received(1).GetByIdAsync(Arg.Any<IClientSessionHandle>(), request.BookingId, Arg.Any<CancellationToken>());
        await _trainCancellationRepository
            .DidNotReceive()
            .CancelTrainReservationAsync(
                Arg.Any<IClientSessionHandle>(),
                request.BookingId,
                Arg.Any<TrainReservation>(),
                Arg.Any<CancellationToken>()
            );
        _timeProvider.DidNotReceive().GetUtcNow();
        await _dispatcher.DidNotReceive().DispatchAsync(Arg.Any<BookingTrainReservationCancelledEvent>(), Arg.Any<string>());
        await _dispatcher.DidNotReceive().DispatchAsync(Arg.Any<BookingCancelledEvent>(), Arg.Any<string>());
    }

    [Fact]
    public async Task HandleSetTrainReservationCancelledAsync_ExpectFalseAndDontDispatchEvents_WhenReservationAlreadyCancelled()
    {
        // Arrange
        const string bookingId = "booking_id";
        const string reservationId = "reservation_id";

        var sut = new TrainReservationService(_trainCancellationRepository, _scope, _dispatcher, _timeProvider, _logger);

        var request = _fixture
            .Build<SetTrainReservationCancelledRequest>()
            .With(i => i.BookingId, bookingId)
            .With(i => i.ReservationId, reservationId)
            .Create();

        var reservation = _fixture.Build<TrainReservation>().With(i => i.ReservationId, reservationId).Create();
        reservation.CancelledAtTimestamp = 123;

        var booking = _fixture.Build<TrainBooking>().With(i => i.BookingId, bookingId).Create();
        booking.Reservations = new[] { reservation };

        _timeProvider.GetUtcNow().Returns(DateTimeOffset.UtcNow);
        _trainCancellationRepository.GetByIdAsync(Arg.Any<IClientSessionHandle>(), request.BookingId, Arg.Any<CancellationToken>()).Returns(booking);

        _trainCancellationRepository
            .CancelTrainReservationAsync(
                Arg.Any<IClientSessionHandle>(),
                request.BookingId,
                Arg.Any<TrainReservation>(),
                Arg.Any<CancellationToken>()
            )
            .Returns(false);

        // Act
        var result = await sut.HandleSetTrainReservationCancelledAsync(_session, request, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result
            .ErrorMessage.Should()
            .Be($"Train reservation id {request.ReservationId} is already cancelled for booking with BookingId: {request.BookingId}");

        await _trainCancellationRepository.Received(1).GetByIdAsync(Arg.Any<IClientSessionHandle>(), request.BookingId, Arg.Any<CancellationToken>());
        await _trainCancellationRepository
            .DidNotReceive()
            .CancelTrainReservationAsync(
                Arg.Any<IClientSessionHandle>(),
                request.BookingId,
                Arg.Any<TrainReservation>(),
                Arg.Any<CancellationToken>()
            );
        _timeProvider.DidNotReceive().GetUtcNow();
        await _dispatcher.DidNotReceive().DispatchAsync(Arg.Any<BookingTrainReservationCancelledEvent>(), Arg.Any<string>());
        await _dispatcher.DidNotReceive().DispatchAsync(Arg.Any<BookingCancelledEvent>(), Arg.Any<string>());
    }

    [Fact]
    public async Task HandleSetTrainReservationCancelledAsync_ExpectFalseAndDontSendEvents_WhenFailToCancelReservation()
    {
        // Arrange
        const string bookingId = "booking_id";
        const string reservationId = "reservation_id";

        var sut = new TrainReservationService(_trainCancellationRepository, _scope, _dispatcher, _timeProvider, _logger);

        var request = _fixture
            .Build<SetTrainReservationCancelledRequest>()
            .With(i => i.BookingId, bookingId)
            .With(i => i.ReservationId, reservationId)
            .Create();

        var reservation = _fixture.Build<TrainReservation>().With(i => i.ReservationId, reservationId).Create();
        reservation.CancelledAtTimestamp = null;

        var booking = _fixture.Build<TrainBooking>().With(i => i.BookingId, bookingId).Create();
        booking.Reservations = new[] { reservation };

        _timeProvider.GetUtcNow().Returns(DateTimeOffset.UtcNow);
        _trainCancellationRepository.GetByIdAsync(Arg.Any<IClientSessionHandle>(), request.BookingId, Arg.Any<CancellationToken>()).Returns(booking);

        _trainCancellationRepository
            .CancelTrainReservationAsync(
                Arg.Any<IClientSessionHandle>(),
                request.BookingId,
                Arg.Any<TrainReservation>(),
                Arg.Any<CancellationToken>()
            )
            .Returns(false);

        // Act
        var result = await sut.HandleSetTrainReservationCancelledAsync(_session, request, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.ErrorMessage.Should().Be($"Train Reservation Id {request.ReservationId} failed to update");

        await _trainCancellationRepository.Received(1).GetByIdAsync(Arg.Any<IClientSessionHandle>(), request.BookingId, Arg.Any<CancellationToken>());
        await _trainCancellationRepository
            .Received(1)
            .CancelTrainReservationAsync(
                Arg.Any<IClientSessionHandle>(),
                request.BookingId,
                Arg.Any<TrainReservation>(),
                Arg.Any<CancellationToken>()
            );
        _timeProvider.Received(1).GetUtcNow();
        await _dispatcher.DidNotReceive().DispatchAsync(Arg.Any<BookingTrainReservationCancelledEvent>(), Arg.Any<string>());
        await _dispatcher.DidNotReceive().DispatchAsync(Arg.Any<BookingCancelledEvent>(), Arg.Any<string>());
    }

    [Fact]
    public async Task HandleSetTrainReservationCancelledAsync_ExpectTrueAndUpdateDatabaseAndSendBookingTrainReservationCancelledEvent_WhenNotAllReservationsAreCancelled()
    {
        // Arrange
        const string bookingId = "booking_id";
        const string reservationId = "reservation_id";

        var sut = new TrainReservationService(_trainCancellationRepository, _scope, _dispatcher, _timeProvider, _logger);

        var request = _fixture
            .Build<SetTrainReservationCancelledRequest>()
            .With(i => i.BookingId, bookingId)
            .With(i => i.ReservationId, reservationId)
            .Create();

        var otherReservation = _fixture.Create<TrainReservation>();
        otherReservation.CancelledAtTimestamp = null;
        var reservation = _fixture.Build<TrainReservation>().With(i => i.ReservationId, reservationId).Create();
        reservation.CancelledAtTimestamp = null;

        var booking = _fixture.Build<TrainBooking>().With(i => i.BookingId, bookingId).Create();
        booking.Reservations = new[] { reservation, otherReservation };

        _timeProvider.GetUtcNow().Returns(DateTimeOffset.UtcNow);
        _trainCancellationRepository.GetByIdAsync(Arg.Any<IClientSessionHandle>(), request.BookingId, Arg.Any<CancellationToken>()).Returns(booking);

        _trainCancellationRepository
            .CancelTrainReservationAsync(
                Arg.Any<IClientSessionHandle>(),
                request.BookingId,
                Arg.Any<TrainReservation>(),
                Arg.Any<CancellationToken>()
            )
            .Returns(true);

        // Act
        var result = await sut.HandleSetTrainReservationCancelledAsync(_session, request, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.ErrorMessage.Should().BeNullOrEmpty();

        await _trainCancellationRepository.Received(1).GetByIdAsync(Arg.Any<IClientSessionHandle>(), request.BookingId, Arg.Any<CancellationToken>());
        await _trainCancellationRepository
            .Received(1)
            .CancelTrainReservationAsync(
                Arg.Any<IClientSessionHandle>(),
                request.BookingId,
                Arg.Any<TrainReservation>(),
                Arg.Any<CancellationToken>()
            );
        _timeProvider.Received(2).GetUtcNow();
        await _dispatcher.Received(1).DispatchAsync(Arg.Any<BookingTrainReservationCancelledEvent>(), Arg.Any<string>());
        await _dispatcher.DidNotReceive().DispatchAsync(Arg.Any<BookingCancelledEvent>(), Arg.Any<string>());
    }

    [Fact]
    public async Task HandleSetTrainReservationCancelledAsync_ExpectTrueAndUpdateDatabaseAndSendBookingTrainReservationCancelledEventAndBookingCancelledEvent_WhenAllReservationsAreCancelled()
    {
        // Arrange
        const string bookingId = "booking_id";
        const string reservationId = "reservation_id";

        var sut = new TrainReservationService(_trainCancellationRepository, _scope, _dispatcher, _timeProvider, _logger);

        var request = _fixture
            .Build<SetTrainReservationCancelledRequest>()
            .With(i => i.BookingId, bookingId)
            .With(i => i.ReservationId, reservationId)
            .Create();

        var reservation = _fixture.Build<TrainReservation>().With(i => i.ReservationId, reservationId).Create();
        reservation.CancelledAtTimestamp = null;

        var booking = _fixture.Build<TrainBooking>().With(i => i.BookingId, bookingId).Create();
        booking.Reservations = new[] { reservation };

        _timeProvider.GetUtcNow().Returns(DateTimeOffset.UtcNow);
        _trainCancellationRepository.GetByIdAsync(Arg.Any<IClientSessionHandle>(), request.BookingId, Arg.Any<CancellationToken>()).Returns(booking);

        _trainCancellationRepository
            .CancelTrainReservationAsync(
                Arg.Any<IClientSessionHandle>(),
                request.BookingId,
                Arg.Any<TrainReservation>(),
                Arg.Any<CancellationToken>()
            )
            .Returns(true);

        // Act
        var result = await sut.HandleSetTrainReservationCancelledAsync(_session, request, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.ErrorMessage.Should().BeNullOrEmpty();

        await _trainCancellationRepository.Received(1).GetByIdAsync(Arg.Any<IClientSessionHandle>(), request.BookingId, Arg.Any<CancellationToken>());
        await _trainCancellationRepository
            .Received(1)
            .CancelTrainReservationAsync(
                Arg.Any<IClientSessionHandle>(),
                request.BookingId,
                Arg.Any<TrainReservation>(),
                Arg.Any<CancellationToken>()
            );
        _timeProvider.Received(3).GetUtcNow();
        await _dispatcher.Received(1).DispatchAsync(Arg.Any<BookingTrainReservationCancelledEvent>(), Arg.Any<string>());
        await _dispatcher.Received(1).DispatchAsync(Arg.Any<BookingCancelledEvent>(), Arg.Any<string>());
    }

    [Fact]
    public async Task HandleSetTrainReservationCancelledFailedRequestAsync_ExpectFalseAndDontDispatchEvent_WhenBookingNotFound()
    {
        // Arrange
        const string bookingId = "booking_id";
        const string reservationId = "reservation_id";

        var sut = new TrainReservationService(_trainCancellationRepository, _scope, _dispatcher, _timeProvider, _logger);

        var request = _fixture
            .Build<SetTrainReservationCancelledFailedRequest>()
            .With(i => i.BookingId, bookingId)
            .With(i => i.ReservationId, reservationId)
            .With(i => i.Code, ErrorCodes.Error)
            .With(i => i.Reason, "Some reason")
            .Create();

        _timeProvider.GetUtcNow().Returns(DateTimeOffset.UtcNow);
        _trainCancellationRepository
            .GetByIdAsync(Arg.Any<IClientSessionHandle>(), request.BookingId, Arg.Any<CancellationToken>())
            .Returns(null as TrainBooking);

        // Act
        var result = await sut.HandleSetTrainReservationCancelledFailedRequestAsync(_session, request, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.ErrorMessage.Should().Be($"There is no train booking record for booking with BookingId: {request.BookingId}");

        await _trainCancellationRepository.Received(1).GetByIdAsync(Arg.Any<IClientSessionHandle>(), request.BookingId, Arg.Any<CancellationToken>());
        _timeProvider.DidNotReceive().GetUtcNow();
        await _dispatcher.DidNotReceive().DispatchAsync(Arg.Any<BookingTrainReservationCancellationFailedEvent>(), Arg.Any<string>());
    }

    [Fact]
    public async Task HandleSetTrainReservationCancelledFailedRequestAsync_ExpectFalseAndDontDispatchEvent_WhenReservationNotFound()
    {
        // Arrange
        const string bookingId = "booking_id";
        const string reservationId = "reservation_id";

        var sut = new TrainReservationService(_trainCancellationRepository, _scope, _dispatcher, _timeProvider, _logger);

        var request = _fixture
            .Build<SetTrainReservationCancelledFailedRequest>()
            .With(i => i.BookingId, bookingId)
            .With(i => i.ReservationId, reservationId)
            .With(i => i.Code, ErrorCodes.Error)
            .With(i => i.Reason, "Some reason")
            .Create();

        var booking = _fixture.Build<TrainBooking>().With(i => i.BookingId, bookingId).Create();

        _timeProvider.GetUtcNow().Returns(DateTimeOffset.UtcNow);
        _trainCancellationRepository.GetByIdAsync(Arg.Any<IClientSessionHandle>(), request.BookingId, Arg.Any<CancellationToken>()).Returns(booking);

        // Act
        var result = await sut.HandleSetTrainReservationCancelledFailedRequestAsync(_session, request, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result
            .ErrorMessage.Should()
            .Be($"There is no train reservation record id {request.ReservationId} for booking with BookingId: {request.BookingId}");

        await _trainCancellationRepository.Received(1).GetByIdAsync(Arg.Any<IClientSessionHandle>(), request.BookingId, Arg.Any<CancellationToken>());
        _timeProvider.DidNotReceive().GetUtcNow();
        await _dispatcher.DidNotReceive().DispatchAsync(Arg.Any<BookingTrainReservationCancellationFailedEvent>(), Arg.Any<string>());
    }

    [Fact]
    public async Task HandleSetTrainReservationCancelledFailedRequestAsync_ExpectTrueAndDispatchBookingTrainReservationCancellationFailedEvent_WhenSuccessful()
    {
        // Arrange
        const string bookingId = "booking_id";
        const string reservationId = "reservation_id";

        var sut = new TrainReservationService(_trainCancellationRepository, _scope, _dispatcher, _timeProvider, _logger);

        var request = _fixture
            .Build<SetTrainReservationCancelledFailedRequest>()
            .With(i => i.BookingId, bookingId)
            .With(i => i.ReservationId, reservationId)
            .With(i => i.Code, ErrorCodes.Error)
            .With(i => i.Reason, "Some reason")
            .Create();

        var reservation = _fixture.Build<TrainReservation>().With(i => i.ReservationId, reservationId).Create();

        var booking = _fixture.Build<TrainBooking>().With(i => i.BookingId, bookingId).Create();
        booking.Reservations = new[] { reservation };

        _timeProvider.GetUtcNow().Returns(DateTimeOffset.UtcNow);
        _trainCancellationRepository.GetByIdAsync(Arg.Any<IClientSessionHandle>(), request.BookingId, Arg.Any<CancellationToken>()).Returns(booking);

        // Act
        var result = await sut.HandleSetTrainReservationCancelledFailedRequestAsync(_session, request, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.ErrorMessage.Should().BeNullOrEmpty();

        await _trainCancellationRepository.Received(1).GetByIdAsync(Arg.Any<IClientSessionHandle>(), request.BookingId, Arg.Any<CancellationToken>());
        _timeProvider.Received(1).GetUtcNow();
        await _dispatcher.Received(1).DispatchAsync(Arg.Any<BookingTrainReservationCancellationFailedEvent>(), Arg.Any<string>());
    }
}
