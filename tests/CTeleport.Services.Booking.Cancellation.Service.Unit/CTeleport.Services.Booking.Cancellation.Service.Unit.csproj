<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <InvariantGlobalization>true</InvariantGlobalization>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\CTeleport.Services.Booking.Cancellation.Service\CTeleport.Services.Booking.Cancellation.Service.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoFixture"/>
    <PackageReference Include="coverlet.collector"/>
    <PackageReference Include="FluentAssertions"/>
    <PackageReference Include="Microsoft.NET.Test.Sdk"/>
    <PackageReference Include="NSubstitute"/>
    <PackageReference Include="SonarAnalyzer.CSharp">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="xunit"/>
    <PackageReference Include="xunit.runner.visualstudio"/>
  </ItemGroup>

  <ItemGroup>
    <Using Include="AutoFixture"/>
    <Using Include="FluentAssertions"/>
    <Using Include="Xunit"/>
    <Using Include="Xunit.Abstractions"/>
  </ItemGroup>

</Project>
