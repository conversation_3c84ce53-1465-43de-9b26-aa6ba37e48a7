services:
  mongo-cteleport:
    image: mongo:7.0
    container_name: mongo-cteleport
    hostname: mongo-cteleport
    restart: unless-stopped
    command: [ "--replSet", "rs-cteleport", "--bind_ip_all", "--port", "27017" ]
    ports:
      - "27017:27017"
    healthcheck:
      test: echo "try { rs.status() } catch (err) { rs.initiate({_id:'rs-cteleport',members:[{_id:0,host:'mongo-cteleport:27017'}]}) }" | mongosh --port 27017 --quiet
      interval: 5s
      timeout: 30s
      start_period: 0s
      retries: 30
    volumes:
      - mongodata:/data/db
      - mongoconfig:/data/configdb

  rabbitmq-cteleport:
    image: rabbitmq:3.6.6-management-alpine
    container_name: rabbitmq-cteleport
    hostname: rabbitmq-cteleport
    restart: unless-stopped
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmqdata:/var/lib/rabbitmq/data

  redis-cteleport:
    image: redis:6.0.9-alpine
    container_name: redis-cteleport
    hostname: redis-cteleport
    restart: unless-stopped
    ports:
      - "6379:6379"

  apm-server-cteleport:
    image: docker.elastic.co/apm/apm-server:7.17.24
    container_name: apm-server-cteleport
    restart: unless-stopped
    depends_on:
      elasticsearch-cteleport:
        condition: service_healthy
    cap_add: [ "CHOWN", "DAC_OVERRIDE", "SETGID", "SETUID" ]
    cap_drop: [ "ALL" ]
    ports:
      - "8200:8200"
    networks:
      - elastic
    command: >
      apm-server -e
        -E apm-server.rum.enabled=true
        -E setup.template.settings.index.number_of_replicas=0
        -E output.elasticsearch.hosts=["elasticsearch-cteleport:9200"]
    healthcheck:
      interval: 10s
      retries: 12
      test: curl --write-out 'HTTP %{http_code}' --fail --silent --output /dev/null http://localhost:8200/

  elasticsearch-cteleport:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.17.24
    container_name: elasticsearch-cteleport
    restart: unless-stopped
    environment:
      - bootstrap.memory_lock=true
      - cluster.name=docker-cluster
      - cluster.routing.allocation.disk.threshold_enabled=false
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms1g -Xmx1g
    ulimits:
      memlock:
        hard: -1
        soft: -1
    volumes:
      - esdata:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - elastic
    healthcheck:
      interval: 20s
      retries: 10
      test: curl -s http://localhost:9200/_cluster/health | grep -vq '"status":"red"'

  kibana-cteleport:
    image: docker.elastic.co/kibana/kibana:7.17.24
    container_name: kibana-cteleport
    depends_on:
      elasticsearch-cteleport:
        condition: service_healthy
    environment:
      ELASTICSEARCH_URL: http://elasticsearch-cteleport:9200
      ELASTICSEARCH_HOSTS: http://elasticsearch-cteleport:9200
    ports:
      - 5601:5601
    networks:
      - elastic
    healthcheck:
      interval: 10s
      retries: 20
      test: curl --write-out 'HTTP %{http_code}' --fail --silent --output /dev/null http://localhost:5601/api/status

volumes:
  mongodata:
  mongoconfig:
  rabbitmqdata:
  esdata:
    driver: local

networks:
  elastic:
    driver: bridge
