<Project>
  <ItemGroup Label="Microsoft">
    <PackageVersion Include="Asp.Versioning.Http" Version="8.1.0" />
    <PackageVersion Include="Asp.Versioning.Mvc.ApiExplorer" Version="8.1.0" />
    <PackageVersion Include="Microsoft.AspNetCore.OpenApi" Version="8.0.8" />
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.2" />
    <PackageVersion Include="Microsoft.Extensions.Http.Polly" Version="8.0.10" />
  </ItemGroup>
  <ItemGroup Label="3rd Party">
    <PackageVersion Include="Autofac.Extensions.DependencyInjection" Version="10.0.0" />
    <PackageVersion Include="FluentValidation.DependencyInjectionExtensions" Version="11.10.0" />
    <PackageVersion Include="MongoDB.Driver" Version="2.22.0" />
    <PackageVersion Include="Polly.Contrib.WaitAndRetry" Version="1.1.1" />
    <PackageVersion Include="Refit" Version="8.0.0" />
    <PackageVersion Include="Refit.HttpClientFactory" Version="8.0.0" />
    <PackageVersion Include="SonarAnalyzer.CSharp" Version="9.32.0.97167" />
    <PackageVersion Include="Stateless" Version="5.16.0" />
    <PackageVersion Include="Swashbuckle.AspNetCore" Version="6.7.3" />
  </ItemGroup>
  <ItemGroup Label="C Teleport">
    <PackageVersion Include="CTeleport.Authorization" Version="1.1.208" />
    <PackageVersion Include="CTeleport.Common.Logging" Version="1.1.112" />
    <PackageVersion Include="CTeleport.Common.V2.Mongo" Version="2025.3.31.73" />
    <PackageVersion Include="CTeleport.Common.V2.Api.Infrastructure" Version="1.1.33" />
    <PackageVersion Include="CTeleport.Common.V2.Messaging.CAP.MongoDB" Version="1.1.191" />
    <PackageVersion Include="CTeleport.Common.V2.Tracing" Version="1.1.20" />
    <PackageVersion Include="CTeleport.HealthChecks.Api" Version="2024.8.21.20" />
    <PackageVersion Include="CTeleport.HealthChecks.Core" Version="2024.11.25.27" />
    <PackageVersion Include="CTeleport.Infrastructure.Http" Version="2025.3.28.62" />
    <PackageVersion Include="CTeleport.Infrastructure.Serilog" Version="2025.3.28.62" />
    <PackageVersion Include="CTeleport.Messages.Abstractions" Version="1.1.3471" />
    <PackageVersion Include="CTeleport.Services.Booking.Aggregate.Messages" Version="2025.6.18.6" />
    <PackageVersion Include="CTeleport.Services.Providers.Messages" Version="2025.5.29.98" />
  </ItemGroup>
  <ItemGroup Label="Testing">
    <PackageVersion Include="AutoFixture" Version="4.18.1" />
    <PackageVersion Include="coverlet.collector" Version="6.0.2" />
    <PackageVersion Include="FluentAssertions" Version="6.12.1" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.8" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.11.1" />
    <PackageVersion Include="NSubstitute" Version="5.1.0" />
    <PackageVersion Include="Testcontainers" Version="4.0.0" />
    <PackageVersion Include="Testcontainers.MongoDb" Version="4.0.0" />
    <PackageVersion Include="Testcontainers.RabbitMq" Version="4.0.0" />
    <PackageVersion Include="Testcontainers.Redis" Version="4.0.0" />
    <PackageVersion Include="xunit" Version="2.9.0" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="2.8.2" />
  </ItemGroup>
</Project>