using <PERSON>;
using Polly.Contrib.WaitAndRetry;

namespace CTeleport.Services.Booking.Cancellation.Client;

public static class Policies
{
    private static Func<HttpResponseMessage, bool> RetryAllowancePredicate =>
        r => !r.IsSuccessStatusCode && (int)r.StatusCode >= 500 && (int)r.StatusCode < 600;

    public static IAsyncPolicy<HttpResponseMessage> GetWaitAndRetryPolicy() =>
        Policy
            .HandleResult(RetryAllowancePredicate)
            .WaitAndRetryAsync(Backoff.DecorrelatedJitterBackoffV2(medianFirstRetryDelay: TimeSpan.FromSeconds(1), retryCount: 3));

    public static IAsyncPolicy<HttpResponseMessage> GetSimpleRetryPolicy() => Policy.HandleResult(RetryAllowancePredicate).RetryAsync(1);

    public static IAsyncPolicy<HttpResponseMessage> GetNoOpPolicy() => Policy.NoOpAsync().AsAsyncPolicy<HttpResponseMessage>();
}
