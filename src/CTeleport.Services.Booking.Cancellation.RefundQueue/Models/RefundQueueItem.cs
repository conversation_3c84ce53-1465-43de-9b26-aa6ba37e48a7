using System.ComponentModel.DataAnnotations;

namespace CTeleport.Services.Booking.Cancellation.RefundQueue.Models
{
    /// <summary>
    /// Represents a refund queue item for train booking refund processing
    /// </summary>
    public class RefundQueueItem
    {
        /// <summary>
        /// Reference to the train booking
        /// </summary>
        [Required]
        public string TrainBookingId { get; set; } = string.Empty;

        /// <summary>
        /// Current state of the refund (pending, processing, completed, failed, manual_review)
        /// </summary>
        [Required]
        public RefundState Status { get; set; }

        /// <summary>
        /// Amount returned by the provider
        /// </summary>
        public decimal? ProviderRefundAmount { get; set; }

        /// <summary>
        /// Final amount to be returned to user
        /// </summary>
        public decimal? CalculatedUserRefund { get; set; }

        /// <summary>
        /// Number of retry attempts
        /// </summary>
        public int AttemptCount { get; set; } = 0;

        /// <summary>
        /// When the refund queue item was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// When the refund queue item was last updated
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Error details for failed attempts
        /// </summary>
        public string? FailureReason { get; set; }

        /// <summary>
        /// Additional context (provider response, calculation details)
        /// </summary>
        public RefundMetadata? Metadata { get; set; }
    }

}
