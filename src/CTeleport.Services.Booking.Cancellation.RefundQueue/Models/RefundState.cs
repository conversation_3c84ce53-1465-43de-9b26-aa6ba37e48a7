namespace CTeleport.Services.Booking.Cancellation.RefundQueue.Models
{
    /// <summary>
    /// Refund processing state enumeration
    /// </summary>
    public enum RefundState
    {
        /// <summary>
        /// Refund is pending and waiting to be processed
        /// </summary>
        Pending,

        /// <summary>
        /// Refund is being processed
        /// </summary>
        Processing,

        /// <summary>
        /// Refund has been completed successfully
        /// </summary>
        Completed,

        /// <summary>
        /// Refund has failed
        /// </summary>
        Failed,
    }
}
