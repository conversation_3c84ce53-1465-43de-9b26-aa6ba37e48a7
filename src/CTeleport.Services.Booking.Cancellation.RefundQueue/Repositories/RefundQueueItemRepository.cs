using CTeleport.Services.Booking.Cancellation.RefundQueue.Models;
using MongoDB.Driver;
using MongoDB.Driver.Linq;

namespace CTeleport.Services.Booking.Cancellation.RefundQueue.Repositories
{
    /// <summary>
    /// MongoDB repository implementation for RefundQueueItem
    /// </summary>
    public class RefundQueueItemRepository(IMongoDatabase database) : IRefundQueueItemRepository
    {
        private readonly IMongoCollection<RefundQueueItem> _collection = database.GetCollection<RefundQueueItem>("refund_queue_items");

        public async Task AddAsync(RefundQueueItem item)
        {
            await _collection.InsertOneAsync(item);
        }

        public async Task<RefundQueueItem?> GetByTrainBookingIdAsync(string trainBookingId)
        {
            return await _collection.AsQueryable()
                .FirstOrDefaultAsync(x => x.TrainBookingId == trainBookingId);
        }

        public async Task<List<RefundQueueItem>> GetAllAsync()
        {
            return await _collection.AsQueryable()
                .OrderByDescending(x => x.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<RefundQueueItem>> GetByStatusAsync(RefundState status)
        {
            return await _collection.AsQueryable()
                .Where(x => x.Status == status)
                .OrderByDescending(x => x.UpdatedAt)
                .ToListAsync();
        }

        public async Task UpdateAsync(RefundQueueItem item)
        {
            item.UpdatedAt = DateTime.UtcNow;
            await _collection.ReplaceOneAsync(x => x.TrainBookingId == item.TrainBookingId, item);
        }

        public async Task DeleteAsync(string trainBookingId)
        {
            await _collection.DeleteOneAsync(x => x.TrainBookingId == trainBookingId);
        }

        public async Task<bool> ExistsAsync(string trainBookingId)
        {
            return await _collection.AsQueryable()
                .AnyAsync(x => x.TrainBookingId == trainBookingId);
        }

        public async Task<List<RefundQueueItem>> GetItemsForRetryAsync(int maxAttempts = 3)
        {
            return await _collection.AsQueryable()
                .Where(x => x.Status == RefundState.Failed && x.AttemptCount < maxAttempts)
                .OrderBy(x => x.UpdatedAt)
                .ToListAsync();
        }

        public async Task<List<RefundQueueItem>> GetUpdatedAfterAsync(DateTime updatedAfter)
        {
            return await _collection.AsQueryable()
                .Where(x => x.UpdatedAt > updatedAfter)
                .OrderByDescending(x => x.UpdatedAt)
                .ToListAsync();
        }
    }
}
