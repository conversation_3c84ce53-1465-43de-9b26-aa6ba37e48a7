using CTeleport.Services.Booking.Cancellation.RefundQueue.Models;

namespace CTeleport.Services.Booking.Cancellation.RefundQueue.Repositories
{
    /// <summary>
    /// Repository interface for RefundQueueItem operations
    /// </summary>
    public interface IRefundQueueItemRepository
    {
        /// <summary>
        /// Add a new refund queue item
        /// </summary>
        Task AddAsync(RefundQueueItem item);

        /// <summary>
        /// Get refund queue item by train booking ID
        /// </summary>
        Task<RefundQueueItem?> GetByTrainBookingIdAsync(string trainBookingId);

        /// <summary>
        /// Get all refund queue items
        /// </summary>
        Task<List<RefundQueueItem>> GetAllAsync();

        /// <summary>
        /// Get refund queue items by status
        /// </summary>
        Task<List<RefundQueueItem>> GetByStatusAsync(RefundState status);

        /// <summary>
        /// Update an existing refund queue item
        /// </summary>
        Task UpdateAsync(RefundQueueItem item);

        /// <summary>
        /// Delete a refund queue item by train booking ID
        /// </summary>
        Task DeleteAsync(string trainBookingId);

        /// <summary>
        /// Check if refund queue item exists for train booking ID
        /// </summary>
        Task<bool> ExistsAsync(string trainBookingId);

        /// <summary>
        /// Get refund queue items that need retry (failed with attempts less than max)
        /// </summary>
        Task<List<RefundQueueItem>> GetItemsForRetryAsync(int maxAttempts = 3);

        /// <summary>
        /// Get refund queue items updated after a specific date
        /// </summary>
        Task<List<RefundQueueItem>> GetUpdatedAfterAsync(DateTime updatedAfter);
    }
}
