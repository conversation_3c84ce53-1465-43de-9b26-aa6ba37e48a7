using CTeleport.Services.Booking.Cancellation.Service.Models.Requests;
using CTeleport.Services.Providers.Messages.Booking.Trains.Events;

namespace CTeleport.Services.Booking.Cancellation.Service.Converters;

public static class SetTrainReservationCancelledFailedRequestConverter
{
    public static SetTrainReservationCancelledFailedRequest ToRequest(this TrainReservationCancellationFailedEvent @event) =>
        new()
        {
            IdempotencyKey = @event.CorrelationId,
            BookingId = @event.BookingId,
            ReservationId = @event.ReservationId,
            Code = @event.Code,
            Reason = @event.Reason,
        };
}
