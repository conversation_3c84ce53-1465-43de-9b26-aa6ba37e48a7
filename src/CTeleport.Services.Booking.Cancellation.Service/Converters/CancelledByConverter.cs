using CTeleport.Services.Booking.Aggregate.Models;
using CTeleport.Services.Booking.Cancellation.Service.Models;

namespace CTeleport.Services.Booking.Cancellation.Service.Converters;

public static class CancelledByConverter
{
    public static CancelledBy ToCancelledBy(this UserInfo userInfo) =>
        new()
        {
            UserIdentity = userInfo.UserIdentity,
            Name = userInfo.Name,
            Email = userInfo.Email,
            Roles = userInfo.Roles,
        };

    public static UserInfo ToUserInfo(this CancelledBy cancelledBy) =>
        new()
        {
            UserIdentity = cancelledBy.UserIdentity,
            Name = cancelledBy.Name,
            Email = cancelledBy.Email,
            Roles = cancelledBy.Roles,
        };
}
