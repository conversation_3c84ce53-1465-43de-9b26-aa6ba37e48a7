using System.Collections.Immutable;
using CTeleport.Services.Booking.Cancellation.Service.Models;
using CTeleport.Services.Providers.Models;
using AggregateTrainReservationInfo = CTeleport.Services.Booking.Aggregate.Models.TrainReservationInfo;

namespace CTeleport.Services.Booking.Cancellation.Service.Converters;

public static class ReservationLocatorInfoConverter
{
    public static IReadOnlyCollection<ReservationLocator> ToReservationLocators(this AggregateTrainReservationInfo? trainReservationInfo)
    {
        return trainReservationInfo?.Locators is null
            ? []
            : trainReservationInfo.Locators.Select(l => new ReservationLocator { Locator = l.Locator, Name = l.Name }).ToImmutableList();
    }

    private static ReservationLocatorInfo ToReservationLocatorInfo(this ReservationLocator source) =>
        new() { Locator = source.Locator, Name = source.Name };

    public static IReadOnlyCollection<ReservationLocatorInfo> ToReservationLocatorInfos(this IReadOnlyCollection<ReservationLocator> sources) =>
        sources.Select(ToReservationLocatorInfo).ToList();
}
