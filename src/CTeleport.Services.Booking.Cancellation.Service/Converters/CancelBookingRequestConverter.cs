using CTeleport.Services.Booking.Cancellation.Messages.Commands;
using CTeleport.Services.Booking.Cancellation.Service.Models.Requests;

namespace CTeleport.Services.Booking.Cancellation.Service.Converters;

public static class CancelBookingRequestConverter
{
    public static CancelBookingRequest ToRequest(this StartBookingCancellationCommand @event) =>
        new()
        {
            BookingId = @event.BookingId,
            CorrelationId = @event.CorrelationId,
            IdempotencyKey = @event.MessageId.ToString(),
            CancelledBy = @event.CancelledBy,
        };
}
