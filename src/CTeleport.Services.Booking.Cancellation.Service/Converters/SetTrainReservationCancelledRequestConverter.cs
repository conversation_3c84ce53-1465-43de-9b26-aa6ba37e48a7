using CTeleport.Services.Booking.Cancellation.Service.Models.Requests;
using CTeleport.Services.Providers.Messages.Booking.Trains.Events;

namespace CTeleport.Services.Booking.Cancellation.Service.Converters;

public static class SetTrainReservationCancelledRequestConverter
{
    public static SetTrainReservationCancelledRequest ToRequest(this TrainReservationCancelledEvent @event) =>
        new()
        {
            IdempotencyKey = @event.CorrelationId,
            BookingId = @event.BookingId,
            ReservationId = @event.ReservationId,
            TimestampUtc = @event.TimestampUtc,
        };
}
