using CTeleport.Services.Booking.Aggregate.Messages.Events;
using CTeleport.Services.Booking.Cancellation.Service.Models;

namespace CTeleport.Services.Booking.Cancellation.Service.Converters;

public static class TrainBookingConverter
{
    public static TrainBooking ToTrainBooking(this BookingCompletedEvent request) =>
        new()
        {
            BookingId = request.BookingId,
            Reservations = request.Booking.TrainReservations.ToTrainReservations(),
            CancelledBy = null,
            CancelledAtTimestamp = null,
        };
}
