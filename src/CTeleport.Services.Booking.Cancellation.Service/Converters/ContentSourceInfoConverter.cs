using CTeleport.Services.Booking.Cancellation.Service.Models;
using CTeleport.Services.Providers.Models;
using AggregateContentSourceInfo = CTeleport.Services.Booking.Aggregate.Models.ContentSourceInfo;

namespace CTeleport.Services.Booking.Cancellation.Service.Converters;

public static class ContentSourceInfoConverter
{
    public static ContentSourceInfo ToContentSourceInfo(this ContentSource source) =>
        new() { Code = source.Code, ProviderType = source.ProviderType };

    public static ContentSource ToContentSource(this AggregateContentSourceInfo contentSource) =>
        new() { Code = contentSource.Code, ProviderType = contentSource.ProviderType };
}
