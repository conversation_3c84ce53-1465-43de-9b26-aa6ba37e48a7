using CTeleport.Services.Booking.Aggregate.Messages.Events;
using CTeleport.Services.Booking.Cancellation.Service.Models.Requests;

namespace CTeleport.Services.Booking.Cancellation.Service.Converters;

public static class CreateTrainBookingRequestConverter
{
    public static CreateTrainBookingRequest ToCreateTrainBookingRequest(this BookingCompletedEvent @event) =>
        new() { TrainBooking = @event.ToTrainBooking() };
}
