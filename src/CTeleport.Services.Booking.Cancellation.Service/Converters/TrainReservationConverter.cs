using CTeleport.Services.Booking.Aggregate.Models;
using CTeleport.Services.Booking.Cancellation.Service.Models;
using CTeleport.Services.Providers.Models.Enums;

namespace CTeleport.Services.Booking.Cancellation.Service.Converters;

public static class TrainReservationConverter
{
    public static IReadOnlyCollection<TrainReservation> ToTrainReservations(this IReadOnlyCollection<TrainReservationInfo> reservations) =>
        reservations.Select(ToTrainReservation).ToList();

    private static TrainReservation ToTrainReservation(this TrainReservationInfo reservation) =>
        new()
        {
            ReservationId = reservation.ReservationId,
            OperationSource = OperationSource.BookingCancellation,
            ContentSource = reservation.ContentSource.ToContentSource(),
            Locators = reservation.ToReservationLocators(),
            CancelledAtTimestamp = null,
        };
}
