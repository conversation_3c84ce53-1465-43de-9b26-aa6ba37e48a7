namespace CTeleport.Services.Booking.Cancellation.Service.Models;

public record TrainReservation
{
    public required string ReservationId { get; init; }
    public required string OperationSource { get; init; }
    public required ContentSource ContentSource { get; init; }
    public required IReadOnlyCollection<ReservationLocator> Locators { get; init; }
    public required long? CancelledAtTimestamp { get; set; }
}
