using CTeleport.Services.Booking.Cancellation.Service.Enums;

namespace CTeleport.Services.Booking.Cancellation.Service.Models;

public record TrainBooking : IBooking
{
    public required string BookingId { get; init; }
    public required IReadOnlyCollection<TrainReservation> Reservations { get; set; }
    public required CancelledBy? CancelledBy { get; set; }
    public required long? CancelledAtTimestamp { get; set; }

    public BookingType BookingType => BookingType.Train;
}
