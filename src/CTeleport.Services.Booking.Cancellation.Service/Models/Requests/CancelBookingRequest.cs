using CTeleport.Services.Booking.Aggregate.Models;

namespace CTeleport.Services.Booking.Cancellation.Service.Models.Requests;

public class CancelBookingRequest : IIdempotencyRequest
{
    public required string BookingId { get; init; }
    public required string IdempotencyKey { get; init; }
    public required string CorrelationId { get; init; }
    public required UserInfo CancelledBy { get; init; }
}
