using CTeleport.Common.Messaging.CAP.MongoDB.Transactions;
using CTeleport.Common.Mongo;
using CTeleport.Services.Booking.Cancellation.RefundQueue.Repositories;
using CTeleport.Services.Booking.Cancellation.Service.Configuration;
using CTeleport.Services.Booking.Cancellation.Service.Repositories;
using CTeleport.Services.Booking.Cancellation.Service.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;

namespace CTeleport.Services.Booking.Cancellation.Service.Utils;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddServices(this IServiceCollection services, IConfiguration configuration) =>
        services
            .Configure<TtlSettings>(configuration.GetSection(TtlSettings.TtlSettingsSectionName))
            .AddSingleton(sp => sp.GetRequiredService<IOptions<TtlSettings>>().Value)
            .AddSingleton(TimeProvider.System)
            .AddScoped<ITransactionScope, TransactionScope>()
            .AddTransient<IIdempotencyService, IdempotencyService>()
            .AddTransient<ICancellationService, CancellationService>()
            .AddTransient<ITrainReservationService, TrainReservationService>();

    public static IServiceCollection AddRepositories(this IServiceCollection services) =>
        services
            .AddTransient<IIdempotencyRepository, IdempotencyRepository>()
            .AddTransient<IMongoRepository, IdempotencyRepository>()
            .AddTransient<ITrainCancellationRepository, TrainCancellationRepository>()
            .AddTransient<IMongoRepository, TrainCancellationRepository>()
            .AddTransient<IRefundQueueItemRepository, RefundQueueItemRepository>();
}
