using CTeleport.Common.Messaging.CAP;
using CTeleport.Services.Booking.Cancellation.Service.Models.Requests;

namespace CTeleport.Services.Booking.Cancellation.Service.Services;

public interface ITrainReservationService
{
    Task<Result> SetTrainReservationCancelledAsync(SetTrainReservationCancelledRequest request, CancellationToken cancellationToken = default);

    Task<Result> FailTrainReservationCancellationAsync(
        SetTrainReservationCancelledFailedRequest request,
        CancellationToken cancellationToken = default
    );
}
