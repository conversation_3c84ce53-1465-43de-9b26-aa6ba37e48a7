using CTeleport.Common.Messaging.CAP;
using CTeleport.Services.Booking.Cancellation.Service.Models.Requests;

namespace CTeleport.Services.Booking.Cancellation.Service.Services;

public interface ICancellationService
{
    Task<Result> CancelBookingAsync(CancelBookingRequest request, CancellationToken cancellationToken = default);
    Task<Result> CreateTrainBookingAsync(CreateTrainBookingRequest request, CancellationToken cancellationToken = default);
}
