using CTeleport.Common.Messaging.CAP;
using CTeleport.Common.Messaging.CAP.MongoDB.Transactions;
using CTeleport.Services.Booking.Cancellation.Service.Models.Requests;
using CTeleport.Services.Booking.Cancellation.Service.Repositories;
using MongoDB.Driver;

namespace CTeleport.Services.Booking.Cancellation.Service.Services;

public class IdempotencyService(IIdempotencyRepository idempotencyRepository, ITransactionScope scope) : IIdempotencyService
{
    public Task<Result> ExecuteWithIdempotencyAsync<TRequest>(
        TRequest request,
        Func<IClientSessionHandle, TRequest, Task<Result>> handle,
        CancellationToken cancellationToken = default
    )
        where TRequest : IIdempotencyRequest =>
        scope.ExecuteAsync(
            async session =>
            {
                if (await idempotencyRepository.ExistAsync(session, request.IdempotencyKey))
                    return Result.Ok();

                await idempotencyRepository.CreateAsync<TRequest>(session, request.IdempotencyKey);
                return await handle(session, request);
            },
            cancellationToken
        );
}
