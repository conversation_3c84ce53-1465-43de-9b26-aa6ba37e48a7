using CTeleport.Common.Messaging.CAP;
using CTeleport.Services.Booking.Cancellation.Service.Models.Requests;
using MongoDB.Driver;

namespace CTeleport.Services.Booking.Cancellation.Service.Services;

public interface IIdempotencyService
{
    Task<Result> ExecuteWithIdempotencyAsync<TRequest>(
        TRequest request,
        Func<IClientSessionHandle, TRequest, Task<Result>> handle,
        CancellationToken cancellationToken = default
    )
        where TRequest : IIdempotencyRequest;
}
