using CTeleport.Common.Messaging;
using CTeleport.Common.Messaging.CAP;
using CTeleport.Common.Messaging.CAP.MongoDB.Transactions;
using CTeleport.Services.Booking.Cancellation.Messages.Events;
using CTeleport.Services.Booking.Cancellation.Service.Converters;
using CTeleport.Services.Booking.Cancellation.Service.Models;
using CTeleport.Services.Booking.Cancellation.Service.Models.Requests;
using CTeleport.Services.Booking.Cancellation.Service.Repositories;
using CTeleport.Services.Providers.Messages.Booking.Trains.Commands;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;

namespace CTeleport.Services.Booking.Cancellation.Service.Services;

public sealed class CancellationService(
    ITrainCancellationRepository trainCancellationRepository,
    IIdempotencyService idempotencyService,
    IMessageDispatcher dispatcher,
    TimeProvider timeProvider,
    ILogger<CancellationService> logger,
    ITransactionScope scope
) : ICancellationService
{
    public Task<Result> CancelBookingAsync(CancelBookingRequest request, CancellationToken cancellationToken = default) =>
        idempotencyService.ExecuteWithIdempotencyAsync(request, HandleCancelBookingAsync, cancellationToken);

    public Task<Result> CreateTrainBookingAsync(CreateTrainBookingRequest request, CancellationToken cancellationToken = default) =>
        scope.ExecuteAsync(async session => await HandleCreateTrainBookingAsync(session, request), cancellationToken);

    internal async Task<Result> HandleCancelBookingAsync(IClientSessionHandle session, CancelBookingRequest request)
    {
        logger.LogInformation("Cancelling booking with BookingId: {BookingId}", request.BookingId);

        var trainBooking = await trainCancellationRepository.GetByIdAsync(session, request.BookingId);

        if (trainBooking is null)
        {
            logger.LogInformation("There is no train booking record for booking with BookingId: {BookingId}", request.BookingId);
            return Result.Fail($"There is no train booking record for booking with BookingId: {request.BookingId}");
        }

        // Update the train booking with cancellation details
        trainBooking.CancelledAtTimestamp = timeProvider.GetUtcNow().ToUnixTimeSeconds();
        trainBooking.CancelledBy = request.CancelledBy.ToCancelledBy();

        bool isSuccessful = await trainCancellationRepository.CancelTrainBookingAsync(session, trainBooking);

        if (!isSuccessful)
        {
            logger.LogError("Train Booking Id {BookingId} failed to update", request.BookingId);
            return Result.Fail($"Train Booking Id {request.BookingId} failed to update");
        }

        foreach (var trainReservation in trainBooking.Reservations)
        {
            logger.LogInformation(
                "Dispatching {Command} request for Reservation Id: {ReservationId}",
                nameof(CancelTrainReservationCommand),
                trainReservation.ReservationId
            );

            var message = CreateCancelTrainReservationCommand(request.CorrelationId, request.BookingId, trainReservation);

            string provider = trainReservation.ContentSource.Code[..2];
            await dispatcher.DispatchAsync(message, provider);
        }

        await DispatchCancellationStartEvent(request);

        return Result.Ok();
    }

    internal async Task<Result> HandleCreateTrainBookingAsync(IClientSessionHandle session, CreateTrainBookingRequest request)
    {
        logger.LogInformation("Creating train booking with BookingId: {BookingId}", request.TrainBooking.BookingId);

        await trainCancellationRepository.CreateAsync(session, request.TrainBooking);

        return Result.Ok();
    }

    private CancelTrainReservationCommand CreateCancelTrainReservationCommand(string correlationId, string bookingId, TrainReservation reservation) =>
        new()
        {
            CorrelationId = correlationId,
            BookingId = bookingId,
            OperationSource = reservation.OperationSource,
            ContentSource = reservation.ContentSource.ToContentSourceInfo(),
            Locators = reservation.Locators.ToReservationLocatorInfos(),
            TimestampUtc = timeProvider.GetUtcNow().ToUnixTimeSeconds(),
            ReservationId = reservation.ReservationId,
        };

    private async Task DispatchCancellationStartEvent(CancelBookingRequest request)
    {
        var cancellationStartedEvent = new BookingCancellationStartedEvent
        {
            BookingId = request.BookingId,
            CorrelationId = request.CorrelationId,
            TimestampUtc = timeProvider.GetUtcNow().ToUnixTimeSeconds(),
        };

        await dispatcher.DispatchAsync(cancellationStartedEvent);
    }
}
