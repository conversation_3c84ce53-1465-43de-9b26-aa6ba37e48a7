using CTeleport.Common.Messaging;
using CTeleport.Common.Messaging.CAP;
using CTeleport.Common.Messaging.CAP.MongoDB.Transactions;
using CTeleport.Services.Booking.Cancellation.Messages.Events;
using CTeleport.Services.Booking.Cancellation.Service.Converters;
using CTeleport.Services.Booking.Cancellation.Service.Models.Requests;
using CTeleport.Services.Booking.Cancellation.Service.Repositories;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;

namespace CTeleport.Services.Booking.Cancellation.Service.Services;

public sealed class TrainReservationService(
    ITrainCancellationRepository trainCancellationRepository,
    ITransactionScope scope,
    IMessageDispatcher dispatcher,
    TimeProvider timeProvider,
    ILogger<TrainReservationService> logger
) : ITrainReservationService
{
    public async Task<Result> SetTrainReservationCancelledAsync(
        SetTrainReservationCancelledRequest request,
        CancellationToken cancellationToken = default
    ) =>
        await scope.ExecuteAsync(
            async session => await HandleSetTrainReservationCancelledAsync(session, request, cancellationToken),
            cancellationToken
        );

    public async Task<Result> FailTrainReservationCancellationAsync(
        SetTrainReservationCancelledFailedRequest request,
        CancellationToken cancellationToken = default
    ) =>
        await scope.ExecuteAsync(
            async session => await HandleSetTrainReservationCancelledFailedRequestAsync(session, request, cancellationToken),
            cancellationToken
        );

    internal async Task<Result> HandleSetTrainReservationCancelledAsync(
        IClientSessionHandle session,
        SetTrainReservationCancelledRequest request,
        CancellationToken cancellationToken = default
    )
    {
        logger.LogInformation("Setting train reservation as Cancelled: {ReservationId}", request.ReservationId);

        var booking = await trainCancellationRepository.GetByIdAsync(session, request.BookingId, cancellationToken);

        if (booking is null)
        {
            logger.LogInformation("There is no train booking record for booking with BookingId: {BookingId}", request.BookingId);
            return Result.Fail($"There is no train booking record for booking with BookingId: {request.BookingId}");
        }

        var reservation = booking.Reservations.SingleOrDefault(i => i.ReservationId == request.ReservationId);

        if (reservation is null)
        {
            logger.LogInformation(
                "There is no train reservation id {ReservationId} record for booking with BookingId: {BookingId}",
                request.ReservationId,
                request.BookingId
            );

            return Result.Fail($"There is no train reservation record id {request.ReservationId} for booking with BookingId: {request.BookingId}");
        }

        if (reservation.CancelledAtTimestamp is not null)
        {
            logger.LogInformation(
                "Train reservation id {ReservationId} is already cancelled for booking with BookingId: {BookingId}",
                request.ReservationId,
                request.BookingId
            );

            return Result.Fail($"Train reservation id {request.ReservationId} is already cancelled for booking with BookingId: {request.BookingId}");
        }

        // Update the train booking with cancellation details
        reservation.CancelledAtTimestamp = timeProvider.GetUtcNow().ToUnixTimeSeconds();

        bool isSuccessful = await trainCancellationRepository.CancelTrainReservationAsync(session, booking.BookingId, reservation, cancellationToken);

        if (!isSuccessful)
        {
            logger.LogError("Train Reservation Id {ReservationId} failed to update", request.ReservationId);
            return Result.Fail($"Train Reservation Id {request.ReservationId} failed to update");
        }

        await dispatcher.DispatchAsync(
            new BookingTrainReservationCancelledEvent
            {
                BookingId = booking.BookingId,
                CancelledBy = booking.CancelledBy?.ToUserInfo() ?? null!,
                CorrelationId = request.IdempotencyKey,
                ReservationId = request.ReservationId,
                TimestampUtc = request.TimestampUtc,
                CancelledAtTimestamp = timeProvider.GetUtcNow().ToUnixTimeSeconds(),
            }
        );

        // If there are reservations that are not yet cancelled, then do not send BookingCancelledEvent
        if (booking.Reservations.Any(i => i.CancelledAtTimestamp is null))
        {
            logger.LogDebug("There are reservations which are not cancelled, skipping publishing {Event}", nameof(BookingCancelledEvent));
            return Result.Ok();
        }

        logger.LogDebug("All reservations have been cancelled, publishing {Event}", nameof(BookingCancelledEvent));

        await dispatcher.DispatchAsync(
            new BookingCancelledEvent
            {
                BookingId = booking.BookingId,
                CancelledBy = booking.CancelledBy?.ToUserInfo() ?? null!,
                CorrelationId = request.IdempotencyKey,
                TimestampUtc = request.TimestampUtc,
                CancelledAtTimestamp = timeProvider.GetUtcNow().ToUnixTimeSeconds(),
            }
        );

        return Result.Ok();
    }

    internal async Task<Result> HandleSetTrainReservationCancelledFailedRequestAsync(
        IClientSessionHandle session,
        SetTrainReservationCancelledFailedRequest request,
        CancellationToken cancellationToken = default
    )
    {
        logger.LogInformation("Handling TrainReservationCancelledFailed: {@Request}", request);

        var booking = await trainCancellationRepository.GetByIdAsync(session, request.BookingId, cancellationToken);

        if (booking is null)
        {
            logger.LogInformation("There is no train booking record for booking with BookingId: {BookingId}", request.BookingId);
            return Result.Fail($"There is no train booking record for booking with BookingId: {request.BookingId}");
        }

        var reservation = booking.Reservations.SingleOrDefault(i => i.ReservationId == request.ReservationId);

        if (reservation is null)
        {
            logger.LogInformation(
                "There is no train reservation id {ReservationId} record for booking with BookingId: {BookingId}",
                request.ReservationId,
                request.BookingId
            );

            return Result.Fail($"There is no train reservation record id {request.ReservationId} for booking with BookingId: {request.BookingId}");
        }

        await dispatcher.DispatchAsync(
            new BookingTrainReservationCancellationFailedEvent
            {
                BookingId = booking.BookingId,
                CorrelationId = request.IdempotencyKey,
                ReservationId = request.ReservationId,
                TimestampUtc = timeProvider.GetUtcNow().ToUnixTimeSeconds(),
                Code = request.Code,
                Reason = request.Reason,
            }
        );

        return Result.Ok();
    }
}
