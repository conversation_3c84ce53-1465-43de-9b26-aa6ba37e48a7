<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <InvariantGlobalization>true</InvariantGlobalization>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
    <WarningsNotAsErrors>NU1902;NU1903</WarningsNotAsErrors>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\CTeleport.Services.Booking.Cancellation.Messages\CTeleport.Services.Booking.Cancellation.Messages.csproj" />
    <ProjectReference Include="..\CTeleport.Services.Booking.Cancellation.RefundQueue\CTeleport.Services.Booking.Cancellation.RefundQueue.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="CTeleport.Authorization" />
    <PackageReference Include="CTeleport.Services.Providers.Messages" />
    <PackageReference Include="CTeleport.Common.V2.Messaging.CAP.MongoDB"/>
    <PackageReference Include="CTeleport.Common.V2.Mongo"/>
    <PackageReference Include="SonarAnalyzer.CSharp">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Stateless"/>
  </ItemGroup>

  <ItemGroup>
    <InternalsVisibleTo Include="CTeleport.Services.Booking.Cancellation.Service.Unit"/>
  </ItemGroup>

</Project>
