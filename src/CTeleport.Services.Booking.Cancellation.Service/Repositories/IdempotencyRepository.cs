using CTeleport.Services.Booking.Cancellation.Service.Configuration;
using CTeleport.Services.Booking.Cancellation.Service.Models;
using MongoDB.Bson;
using MongoDB.Driver;

namespace CTeleport.Services.Booking.Cancellation.Service.Repositories;

public class IdempotencyRepository(IMongoDatabase database, TtlSettings settings, TimeProvider timeProvider) : IIdempotencyRepository
{
    private const string CollectionName = "idempotency-entries";

    private readonly IMongoCollection<IdempotencyEntry> _collection = database.GetCollection<IdempotencyEntry>(CollectionName);

    public Task CreateAsync<T>(IClientSessionHandle session, string key) =>
        _collection.InsertOneAsync(
            session,
            new IdempotencyEntry
            {
                Id = ObjectId.GenerateNewId(),
                IdempotencyKey = key,
                TriggerType = typeof(T).Name,
                CreatedAt = timeProvider.GetUtcNow().UtcDateTime,
            }
        );

    public Task<bool> ExistAsync(IClientSessionHandle session, string key) => _collection.Find(session, e => e.IdempotencyKey.Equals(key)).AnyAsync();

    public async Task EnsureIndexes()
    {
        await _collection.Indexes.CreateOneAsync(
            new CreateIndexModel<IdempotencyEntry>(
                Builders<IdempotencyEntry>.IndexKeys.Ascending(e => e.IdempotencyKey),
                new CreateIndexOptions { Unique = true }
            )
        );

        await _collection.Indexes.CreateOneAsync(
            new CreateIndexModel<IdempotencyEntry>(
                Builders<IdempotencyEntry>.IndexKeys.Ascending(e => e.CreatedAt),
                new CreateIndexOptions { ExpireAfter = TimeSpan.FromDays(settings.IdempotencyEntryTtlInDays) }
            )
        );
    }
}
