using CTeleport.Common.Mongo;
using CTeleport.Services.Booking.Cancellation.Service.Models;
using MongoDB.Driver;

namespace CTeleport.Services.Booking.Cancellation.Service.Repositories;

public interface ITrainCancellationRepository : IMongoRepository
{
    Task CreateAsync(IClientSessionHandle session, TrainBooking canceledTrainBooking, CancellationToken cancellationToken = default);
    Task<TrainBooking?> GetByIdAsync(IClientSessionHandle session, string id, CancellationToken cancellationToken = default);
    Task<bool> CancelTrainBookingAsync(IClientSessionHandle session, TrainBooking trainBooking, CancellationToken cancellationToken = default);
    Task<bool> CancelTrainReservationAsync(
        IClientSessionHandle session,
        string trainBookingId,
        TrainReservation reservation,
        CancellationToken cancellationToken = default
    );
}
