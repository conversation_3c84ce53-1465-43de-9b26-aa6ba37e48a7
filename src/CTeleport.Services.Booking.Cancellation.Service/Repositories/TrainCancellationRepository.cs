using CTeleport.Services.Booking.Cancellation.Service.Models;
using MongoDB.Driver;
using MongoDB.Driver.Linq;

namespace CTeleport.Services.Booking.Cancellation.Service.Repositories;

public class TrainCancellationRepository(IMongoDatabase database) : ITrainCancellationRepository
{
    private const string CollectionName = "canceled-bookings";

    private readonly IMongoCollection<TrainBooking> _collection = database.GetCollection<TrainBooking>(CollectionName);

    public async Task CreateAsync(IClientSessionHandle session, TrainBooking canceledTrainBooking, CancellationToken cancellationToken = default) =>
        await _collection.InsertOneAsync(session, canceledTrainBooking, cancellationToken: cancellationToken);

    public async Task<TrainBooking?> GetByIdAsync(IClientSessionHandle session, string id, CancellationToken cancellationToken = default) =>
        await _collection.Find(session, t => t.BookingId.Equals(id)).SingleOrDefaultAsync(cancellationToken);

    public async Task<bool> CancelTrainBookingAsync(
        IClientSessionHandle session,
        TrainBooking trainBooking,
        CancellationToken cancellationToken = default
    )
    {
        var filter = Builders<TrainBooking>.Filter.Eq(t => t.BookingId, trainBooking.BookingId);

        var updateDefinition = Builders<TrainBooking>
            .Update.Set(t => t.CancelledBy, trainBooking.CancelledBy)
            .Set(t => t.CancelledAtTimestamp, trainBooking.CancelledAtTimestamp);

        var result = await _collection.UpdateOneAsync(session, filter, updateDefinition, cancellationToken: cancellationToken);

        return result.ModifiedCount is 1;
    }

    public async Task<bool> CancelTrainReservationAsync(
        IClientSessionHandle session,
        string trainBookingId,
        TrainReservation reservation,
        CancellationToken cancellationToken = default
    )
    {
        var filter =
            Builders<TrainBooking>.Filter.Eq(t => t.BookingId, trainBookingId)
            & Builders<TrainBooking>.Filter.ElemMatch(
                x => x.Reservations,
                Builders<TrainReservation>.Filter.Eq(x => x.ReservationId, reservation.ReservationId)
            );

        var updateDefinition = Builders<TrainBooking>.Update.Set(
            i => i.Reservations.FirstMatchingElement().CancelledAtTimestamp,
            reservation.CancelledAtTimestamp
        );

        var result = await _collection.UpdateOneAsync(session, filter, updateDefinition, cancellationToken: cancellationToken);

        return result.ModifiedCount is 1;
    }

    public async Task EnsureIndexes()
    {
        await _collection.Indexes.CreateOneAsync(new CreateIndexModel<TrainBooking>(Builders<TrainBooking>.IndexKeys.Ascending(p => p.BookingId)));
    }
}
