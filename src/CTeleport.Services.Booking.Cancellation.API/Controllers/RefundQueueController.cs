using CTeleport.Services.Booking.Cancellation.RefundQueue.Models;
using CTeleport.Services.Booking.Cancellation.RefundQueue.Repositories;

namespace CTeleport.Services.Booking.Cancellation.API.Controllers;

/// <summary>
/// Controller for managing refund queue operations
/// </summary>
[ApiController]
[Route("admin/refunds/queue")]
[ApiVersion("1.0")]
public class RefundQueueController : ControllerBase
{
    private readonly IRefundQueueItemRepository _repository;
    private readonly ILogger<RefundQueueController> _logger;

    public RefundQueueController(IRefundQueueItemRepository repository, ILogger<RefundQueueController> logger)
    {
        _repository = repository;
        _logger = logger;
    }

    /// <summary>
    /// Get all pending refund queue items
    /// </summary>
    /// <returns>List of pending refund queue items</returns>
    [HttpGet]
    [ProducesResponseType<List<RefundQueueItem>>(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<List<RefundQueueItem>>> GetPendingItems()
    {
        try
        {
            _logger.LogInformation("Getting pending refund queue items");
            var pendingItems = await _repository.GetByStatusAsync(RefundState.Pending);

            _logger.LogInformation("Retrieved {Count} pending refund queue items", pendingItems.Count);
            return Ok(pendingItems);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving pending refund queue items");
            return StatusCode(StatusCodes.Status500InternalServerError, "Internal server error");
        }
    }

    /// <summary>
    /// Get detailed information about a specific refund queue item
    /// </summary>
    /// <param name="id">Train booking ID</param>
    /// <returns>Detailed refund queue item information</returns>
    [HttpGet("{id}")]
    [ProducesResponseType<RefundQueueItem>(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<RefundQueueItem>> GetRefundQueueItem(string id)
    {
        try
        {
            _logger.LogInformation("Getting refund queue item for train booking ID: {TrainBookingId}", id);

            var item = await _repository.GetByTrainBookingIdAsync(id);

            if (item == null)
            {
                _logger.LogWarning("Refund queue item not found for train booking ID: {TrainBookingId}", id);
                return NotFound($"Refund queue item not found for train booking ID: {id}");
            }

            _logger.LogInformation("Retrieved refund queue item for train booking ID: {TrainBookingId}", id);
            return Ok(item);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving refund queue item for train booking ID: {TrainBookingId}", id);
            return StatusCode(StatusCodes.Status500InternalServerError, "Internal server error");
        }
    }
}
