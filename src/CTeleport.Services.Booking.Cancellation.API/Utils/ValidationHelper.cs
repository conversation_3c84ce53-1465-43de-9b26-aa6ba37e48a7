using FluentValidation.Results;

namespace CTeleport.Services.Booking.Cancellation.API.Utils;

public static class ValidationHelper
{
    public static ProblemDetails ProduceProblemDetails(ValidationResult validationResult)
    {
        const string title = "One or more validation errors occurred";

        var problemDetails = new HttpValidationProblemDetails
        {
            Title = title,
            Status = StatusCodes.Status400BadRequest,
            Errors = validationResult.ToDictionary(),
        };

        return problemDetails;
    }
}
