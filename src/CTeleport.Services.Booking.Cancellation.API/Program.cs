using Autofac.Extensions.DependencyInjection;
using CTeleport.Common.Api.Infrastracture;
using CTeleport.Common.Logging.Extensions;
using CTeleport.Common.Messaging.CAP.HealthCheck;
using CTeleport.Common.Mongo;
using CTeleport.Common.Mongo.HealthCheck;
using CTeleport.Common.Redis;
using CTeleport.Common.Tracing;
using CTeleport.HealthChecks.Api;
using CTeleport.HealthChecks.Core;
using CTeleport.Infrastructure;
using CTeleport.Infrastructure.Http;
using CTeleport.Infrastructure.Serilog;
using CTeleport.Services.Booking.Cancellation.API.Middlewares;
using CTeleport.Services.Booking.Cancellation.Messaging.Utils;
using CTeleport.Services.Booking.Cancellation.Service.Utils;
using Elastic.Apm.DiagnosticSource;
using Elastic.Apm.MongoDb;
using FluentValidation;

namespace CTeleport.Services.Booking.Cancellation.API;

public class Program
{
    protected Program() { }

    public static async Task<int> Main(string[] _)
    {
        try
        {
            var builder = WebApplication.CreateBuilder();
            var services = builder.Services;

            services
                .AddMongo(builder.Configuration)
                .AddRedisWithAutoScalePooling(builder.Configuration)
                .AddLogging(builder.Configuration)
                .AddValidatorsFromAssemblyContaining(typeof(Program));

            services.AddControllers();

            services
                .AddEndpointsApiExplorer()
                .AddSwaggerGen(options =>
                {
                    options.CustomSchemaIds(type => type.FullName);
                })
                .AddProblemDetails()
                .AddServices(builder.Configuration)
                .AddRepositories()
                .AddHandlers(builder.Configuration)
                .AddExceptionHandler<GlobalExceptionHandler>()
                .AddApmMetricsCollector(builder.Configuration)
                .AddHttpContextAccessor()
                .AddTraceContext()
                .AddTraceparentHeaderPropagation()
                .AddAuthorization()
                .AddAuths(builder.Configuration)
                // .AddDefaultServiceContext()
                .AddJsonSerializer();

            services
                .AddApiVersioning(options =>
                {
                    options.ReportApiVersions = true;
                    options.AssumeDefaultVersionWhenUnspecified = true;
                    options.DefaultApiVersion = new ApiVersion(1, 0);
                })
                .AddApiExplorer(options =>
                {
                    options.GroupNameFormat = Constants.ApiVersionGroupNameFormat;
                    options.SubstituteApiVersionInUrl = true;
                });

            services.AddHealthChecksWithPublisher().AddApi(builder.Configuration).AddMongoDb(builder.Configuration).AddCAP(builder.Configuration);

            var builderHost = builder.Host;

            builderHost
                .ConfigureHost(builder.Configuration)
                .UseDefaultServiceProvider(options =>
                {
                    options.ValidateScopes = true;
                    options.ValidateOnBuild = true;
                })
                .UseServiceProviderFactory(new AutofacServiceProviderFactory())
                .UseLogging(c => c.Enrich.WithTraceContext());

            var application = builder.Build();

            application.MapHealthChecks();

            if (application.Environment.IsDevelopment())
            {
                application.UseDeveloperExceptionPage();
                SwaggerBuilderExtensions.UseSwagger(application).UseSwaggerUI();
            }

            application
                .UseTraceContext()
                .UseStatusCodePages()
                .UseExceptionHandler()
                .UseApm(builder.Configuration, new HttpDiagnosticsSubscriber(), new MongoDbDiagnosticsSubscriber())
                .UseAuths()
                .UseResponseCaching();

            application.MapControllers();

            await application.RunAsync();
            return 0;
        }
        catch (Exception ex)
        {
            await ServiceBootstrapExceptionHandler.PrintExceptionAsync(ex);
            return 1;
        }
    }
}
