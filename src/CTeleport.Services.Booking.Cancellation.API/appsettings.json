{"MongoDb": {"ConnectionString": "mongodb://localhost:27017/?directConnection=true", "Database": "cteleport-booking-cancellation"}, "RabbitMq": {"Hosts": "localhost", "Username": "guest", "Password": "guest", "ExchangeName": "cap.hotels", "Port": 5672, "ApiPort": 15672}, "Cap": {"EnableSubscriberParallelExecute": false, "EnablePublishParallelSend": false}, "Redis": {"ConnectionString": "localhost:6379", "UseSentinel": "false", "MasterName": "redis"}, "Security": {"Auth0": {"ClientId": "B1ExeYqqeM9NLkvZQSCZ953GAGtB7unT,37Jo0TnyMzsUYOmXNYrQkuL4NDRf7uLQ,u5rrPA9uuqdl3SlX7Ppz4t8ZLRwon9vR,1Iifqr8tPL8f9uIiCgHdwNAyjNgYM1pW", "Domain": "c-teleport-dev.eu.auth0.com", "Namespace": "http://cteleport.com/"}}, "Serilog": {"MinimumLevel": "Debug"}, "Sentry": {"DSN": "https://a0f68733e32b48718b8d663f79dca61c:<EMAIL>/166193"}, "ElasticApm": {"Enabled": false}, "HealthChecks": {"Api": {"Services": {}}, "CAP": {"Enabled": true}, "MongoDb": {"Enabled": true}}, "AllowedHosts": "*", "TtlSettings": {"IdempotencyEntryTtlInDays": 1}}