namespace CTeleport.Services.Booking.Cancellation.API.Middlewares;

public class GlobalExceptionHandler(ILogger<GlobalExceptionHandler> logger) : IExceptionHandler
{
    public async ValueTask<bool> TryHandleAsync(HttpContext httpContext, Exception exception, CancellationToken cancellationToken)
    {
        logger.LogError(exception, "An error occurred. {Message}", exception.Message);

        var problemDetails = new ProblemDetails { Instance = httpContext.Request.Path };

        switch (exception)
        {
            case FluentValidation.ValidationException fluentException:
            {
                httpContext.Response.StatusCode = StatusCodes.Status400BadRequest;
                problemDetails.Title = "One or more validation errors occurred";
                problemDetails.Extensions.Add("Errors", fluentException.Errors.Select(error => error.ErrorMessage).ToList());
                break;
            }
            case BadHttpRequestException badHttpRequestException:
            {
                httpContext.Response.StatusCode = StatusCodes.Status400BadRequest;
                problemDetails.Title = "Bad request";
                problemDetails.Detail = badHttpRequestException.Message;
                break;
            }
            default:
            {
                problemDetails.Title = "Something went wrong";
                problemDetails.Detail = exception.Message;
                break;
            }
        }

        problemDetails.Status = httpContext.Response.StatusCode;

        await httpContext.Response.WriteAsJsonAsync(problemDetails, cancellationToken);

        return true;
    }
}
