<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <InvariantGlobalization>true</InvariantGlobalization>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
    <WarningsNotAsErrors>NU1902;NU1903</WarningsNotAsErrors>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\CTeleport.Services.Booking.Cancellation.Messaging\CTeleport.Services.Booking.Cancellation.Messaging.csproj" />
    <ProjectReference Include="..\CTeleport.Services.Booking.Cancellation.RefundQueue\CTeleport.Services.Booking.Cancellation.RefundQueue.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Asp.Versioning.Http"/>
    <PackageReference Include="Asp.Versioning.Mvc.ApiExplorer"/>
    <PackageReference Include="Autofac.Extensions.DependencyInjection"/>
    <PackageReference Include="CTeleport.Common.Logging"/>
    <PackageReference Include="CTeleport.Common.V2.Api.Infrastructure"/>
    <PackageReference Include="CTeleport.Common.V2.Tracing"/>
    <PackageReference Include="CTeleport.HealthChecks.Api"/>
    <PackageReference Include="CTeleport.HealthChecks.Core"/>
    <PackageReference Include="CTeleport.Infrastructure.Http"/>
    <PackageReference Include="CTeleport.Infrastructure.Serilog"/>
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions"/>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi"/>
    <PackageReference Include="SonarAnalyzer.CSharp">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Swashbuckle.AspNetCore"/>
  </ItemGroup>

  <ItemGroup>
    <Using Include="Asp.Versioning"/>
    <Using Include="Microsoft.AspNetCore.Diagnostics"/>
    <Using Include="Microsoft.AspNetCore.Http.HttpResults"/>
    <Using Include="Microsoft.AspNetCore.Mvc"/>
    <Using Include="Microsoft.Extensions.Configuration"/>
    <Using Include="Microsoft.Extensions.DependencyInjection"/>
    <Using Include="Microsoft.Extensions.Options"/>
    <Using Include="Microsoft.OpenApi.Models"/>
  </ItemGroup>

</Project>
