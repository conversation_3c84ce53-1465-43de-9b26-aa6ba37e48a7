using CTeleport.Common.Messaging;
using CTeleport.Common.Messaging.CAP.MongoDB;
using CTeleport.Services.Booking.Cancellation.Messaging.Commands;
using CTeleport.Services.Booking.Cancellation.Messaging.Events;
using FluentValidation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace CTeleport.Services.Booking.Cancellation.Messaging.Utils;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddHandlers(this IServiceCollection services, IConfiguration configuration) =>
        services
            .AddMessaging(configuration)
            .AddValidatorsFromAssemblyContaining(typeof(ServiceCollectionExtensions))
            .AddTransient<IMessageHandler, StartBookingCancellationCommandHandler>()
            .AddTransient<IMessageHandler, BookingCompletedEventHandler>()
            .AddTransient<IMessageHandler, TrainReservationCancellationFailedEventHandler>()
            .AddTransient<IMessageHand<PERSON>, TrainReservationCancelledEventHandler>();
}
