using CTeleport.Common.Messaging;
using CTeleport.Common.Messaging.Handler;
using CTeleport.Services.Booking.Cancellation.Messages.Commands;
using CTeleport.Services.Booking.Cancellation.Messages.Events;
using CTeleport.Services.Booking.Cancellation.Service.Constants;
using CTeleport.Services.Booking.Cancellation.Service.Converters;
using CTeleport.Services.Booking.Cancellation.Service.Services;
using FluentValidation;
using Microsoft.Extensions.Logging;

namespace CTeleport.Services.Booking.Cancellation.Messaging.Commands;

public class StartBookingCancellationCommandHandler(
    IHandlerFactory handlerFactory,
    ICancellationService service,
    IMessageDispatcher dispatcher,
    IValidator<StartBookingCancellationCommand> validator,
    ILogger<StartBookingCancellationCommand> logger,
    TimeProvider timeProvider
) : IMessageHandler<StartBookingCancellationCommand>
{
    public async Task Handle(StartBookingCancellationCommand command, CancellationToken cancellationToken)
    {
        await handlerFactory
            .Create(command)
            .Run(async context =>
            {
                logger.LogInformation(
                    "Handling {Event} event for Booking Id: {BookingId}",
                    nameof(StartBookingCancellationCommand),
                    context.BookingId
                );

                var validationResult = await validator.ValidateAsync(context, cancellationToken);
                if (!validationResult.IsValid)
                {
                    await DispatchFailedEvent(validationResult.ToString());
                    return;
                }

                var result = await service.CancelBookingAsync(context.ToRequest(), cancellationToken);

                if (result.IsFailure)
                {
                    await DispatchFailedEvent(result.ErrorMessage);
                    return;
                }

                logger.LogInformation("Command {CancelBookingCommand} executed successfully", nameof(StartBookingCancellationCommand));
            })
            .OnError(async e => await DispatchFailedEvent(e.Message))
            .Lock($"booking/{command.BookingId}")
            .ExecuteAsync();

        Task DispatchFailedEvent(string message) =>
            dispatcher.DispatchAsync(
                new BookingCancellationFailedEvent
                {
                    BookingId = command.BookingId,
                    CorrelationId = command.CorrelationId,
                    Code = ErrorCodes.Error,
                    Reason = message,
                    TimestampUtc = timeProvider.GetUtcNow().ToUnixTimeSeconds(),
                }
            );
    }
}
