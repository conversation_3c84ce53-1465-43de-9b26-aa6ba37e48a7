using CTeleport.Services.Providers.Messages.Booking.Trains.Events;
using FluentValidation;

namespace CTeleport.Services.Booking.Cancellation.Messaging.Validators;

public class TrainReservationCancelledEventValidator : AbstractValidator<TrainReservationCancelledEvent>
{
    public TrainReservationCancelledEventValidator()
    {
        RuleFor(c => c.BookingId).NotEmpty();
        RuleFor(c => c.CorrelationId).NotEmpty();
        RuleFor(c => c.TimestampUtc).NotEmpty();
    }
}
