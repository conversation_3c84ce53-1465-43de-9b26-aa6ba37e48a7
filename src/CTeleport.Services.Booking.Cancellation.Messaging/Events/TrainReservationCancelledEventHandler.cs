using CTeleport.Common.Messaging;
using CTeleport.Common.Messaging.Handler;
using CTeleport.Services.Booking.Cancellation.Messages.Events;
using CTeleport.Services.Booking.Cancellation.Service.Constants;
using CTeleport.Services.Booking.Cancellation.Service.Converters;
using CTeleport.Services.Booking.Cancellation.Service.Services;
using CTeleport.Services.Providers.Messages.Booking.Trains.Events;
using CTeleport.Services.Providers.Models.Enums;
using FluentValidation;
using Microsoft.Extensions.Logging;

namespace CTeleport.Services.Booking.Cancellation.Messaging.Events;

public sealed class TrainReservationCancelledEventHandler(
    IHandlerFactory handlerFactory,
    ITrainReservationService service,
    IMessageDispatcher dispatcher,
    IValidator<TrainReservationCancelledEvent> validator,
    ILogger<TrainReservationCancelledEvent> logger
) : IMessageHandler<TrainReservationCancelledEvent>, IMessageHandlerConfiguration
{
    public string Prefix => OperationSource.BookingCancellation;

    public async Task Handle(TrainReservationCancelledEvent message, CancellationToken cancellationToken)
    {
        await handlerFactory
            .Create(message)
            .Run(async context =>
            {
                logger.LogInformation(
                    "Handling {Event} event for reservation Id {ReservationId} with Booking Id: {BookingId}",
                    nameof(TrainReservationCancelledEvent),
                    context.ReservationId,
                    context.BookingId
                );

                var validationResult = await validator.ValidateAsync(context, cancellationToken);
                if (!validationResult.IsValid)
                {
                    logger.LogError("Validation failed for {Event} event: {Errors}", nameof(TrainReservationCancelledEvent), validationResult.Errors);
                    await DispatchFailedEvent(validationResult.ToString());
                    return;
                }

                var result = await service.SetTrainReservationCancelledAsync(context.ToRequest(), cancellationToken);

                if (result.IsFailure)
                {
                    await DispatchFailedEvent(validationResult.ToString());
                    return;
                }

                logger.LogInformation("Event {Event} executed successfully", nameof(TrainReservationCancelledEvent));
            })
            .OnError(async e => await DispatchFailedEvent(e.Message))
            .Lock($"booking/{message.BookingId}")
            .ExecuteAsync();

        Task DispatchFailedEvent(string errorMessage) =>
            dispatcher.DispatchAsync(
                new BookingTrainReservationCancellationFailedEvent
                {
                    BookingId = message.BookingId,
                    CorrelationId = message.CorrelationId,
                    ReservationId = message.ReservationId,
                    TimestampUtc = message.TimestampUtc,
                    Code = ErrorCodes.Error,
                    Reason = errorMessage,
                }
            );
    }
}
