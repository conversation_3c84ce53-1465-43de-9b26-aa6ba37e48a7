using CTeleport.Common.Messaging;
using CTeleport.Common.Messaging.Handler;
using CTeleport.Services.Booking.Cancellation.Service.Converters;
using CTeleport.Services.Booking.Cancellation.Service.Services;
using CTeleport.Services.Providers.Messages.Booking.Trains.Events;
using Microsoft.Extensions.Logging;

namespace CTeleport.Services.Booking.Cancellation.Messaging.Events;

public sealed class TrainReservationCancellationFailedEventHandler(
    IHandlerFactory handlerFactory,
    ITrainReservationService service,
    ILogger<TrainReservationCancellationFailedEvent> logger
) : IMessageHandler<TrainReservationCancellationFailedEvent>
{
    public async Task Handle(TrainReservationCancellationFailedEvent message, CancellationToken cancellationToken)
    {
        await handlerFactory
            .Create(message)
            .Run(async context =>
            {
                logger.LogInformation(
                    "Handling {Event} event for reservation Id {ReservationId} with Booking Id: {BookingId}",
                    nameof(TrainReservationCancellationFailedEvent),
                    context.ReservationId,
                    context.BookingId
                );

                var result = await service.FailTrainReservationCancellationAsync(context.ToRequest(), cancellationToken);

                if (result.IsFailure)
                    throw new InvalidOperationException(result.ErrorMessage);

                logger.LogInformation("Event {Event} executed successfully", nameof(TrainReservationCancellationFailedEvent));
            })
            .Lock($"booking/{message.BookingId}")
            .ExecuteAsync();
    }
}
