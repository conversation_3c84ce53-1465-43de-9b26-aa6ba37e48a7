using CTeleport.Common.Messaging;
using CTeleport.Common.Messaging.Handler;
using CTeleport.Services.Booking.Aggregate.Messages.Events;
using CTeleport.Services.Booking.Cancellation.Service.Converters;
using CTeleport.Services.Booking.Cancellation.Service.Services;
using Microsoft.Extensions.Logging;

namespace CTeleport.Services.Booking.Cancellation.Messaging.Events;

public class BookingCompletedEventHandler(IHandlerFactory handlerFactory, ICancellationService service, ILogger<BookingCompletedEventHandler> logger)
    : IMessageHandler<BookingCompletedEvent>
{
    public async Task Handle(BookingCompletedEvent @event, CancellationToken cancellationToken)
    {
        await handlerFactory
            .Create(@event)
            .Run(async context =>
            {
                logger.LogInformation("Handling {Event} event for Booking Id: {BookingId}", nameof(BookingCompletedEvent), context.BookingId);

                var result = await service.CreateTrainBookingAsync(context.ToCreateTrainBookingRequest(), cancellationToken);

                if (result.IsFailure)
                    throw new InvalidOperationException(result.ErrorMessage);

                logger.LogInformation(
                    "Event {Type} handled successfully. Booking ID: {BookingId}.",
                    nameof(BookingCompletedEvent),
                    context.BookingId
                );
            })
            .Lock($"booking/{@event.BookingId}")
            .ExecuteAsync();
    }
}
