<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <InvariantGlobalization>true</InvariantGlobalization>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
    <WarningsNotAsErrors>NU1902;NU1903</WarningsNotAsErrors>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\CTeleport.Services.Booking.Cancellation.Service\CTeleport.Services.Booking.Cancellation.Service.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="CTeleport.Services.Providers.Messages" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions"/>
    <PackageReference Include="SonarAnalyzer.CSharp">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

</Project>
