FROM mcr.microsoft.com/dotnet/sdk:8.0-alpine AS build
WORKDIR /
COPY NuGet.Config .
COPY Directory.Packages.props .
COPY CTeleport.Services.Booking.Cancellation.sln .
# Copy the main source project files
COPY src/*/*.csproj ./src/
RUN for file in $(ls ./src/*.csproj); do mkdir -p ${file%.*}/ && mv $file ${file%.*}/; done
# Copy the test project files
COPY tests/*/*.csproj ./tests/
RUN for file in $(ls ./tests/*.csproj); do mkdir -p /${file%.*}/ && mv $file /${file%.*}/; done

ENV NUGET_XMLDOC_MODE=none
ARG GITHUB_NUGET_REGISTRY_USERNAME
ARG GITHUB_NUGET_REGISTRY_PASSWORD

RUN dotnet restore CTeleport.Services.Booking.Cancellation.sln --no-cache
COPY src/ ./src/
COPY tests/ ./tests/
RUN dotnet build "/src/CTeleport.Services.Booking.Cancellation.API/CTeleport.Services.Booking.Cancellation.API.csproj" -c Release --no-restore

FROM build AS testrunner
WORKDIR /
ENV TEAMCITY_VERSION=2018.1
RUN dotnet test CTeleport.Services.Booking.Cancellation.sln --no-restore --filter "FullyQualifiedName!~Integration"

FROM testrunner AS publish
RUN dotnet publish "/src/CTeleport.Services.Booking.Cancellation.API/CTeleport.Services.Booking.Cancellation.API.csproj" -c Release -o /app --no-build --no-restore

FROM mcr.microsoft.com/dotnet/aspnet:8.0-alpine AS runtime
RUN apk --no-cache add curl
WORKDIR /app

RUN sed 's/DEFAULT@SECLEVEL=2/DEFAULT@SECLEVEL=1/' /etc/ssl/openssl.cnf > /etc/ssl/openssl.cnf.changed \
&& mv /etc/ssl/openssl.cnf.changed /etc/ssl/openssl.cnf

COPY --from=publish /app .
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 CMD curl --fail -s http://localhost:5500/healthcheck || exit 1
ENV ASPNETCORE_HTTP_PORTS 5500
ENV ASPNETCORE_ENVIRONMENT production
ENV ASPNETCORE_SHUTDOWNTIMEOUTSECONDS 120
ENTRYPOINT ["dotnet", "CTeleport.Services.Booking.Cancellation.API.dll"]
