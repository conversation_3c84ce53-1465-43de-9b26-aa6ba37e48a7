name: "Release: NuGet Package"
on:
  push:
    branches:
      - "*"
  workflow_dispatch: {}
permissions:
  contents: write
  packages: write
jobs:
  build-nuget:
    name: Build NuGet Package
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: NuGet Package
        uses: c-teleport/common-gh-actions/dotnet/nuget/package@v2
        with:
          dotnet-version: 8.0.x
          github-token: ${{ secrets.PACKAGE_TOKEN }}
          solution-directory: ./
