name: "Security: Dependency Submission"
permissions:
  contents: write
  id-token: write
on:
  push:
    branches:
      - master
  schedule:
    - cron: 0 2 * * *
  workflow_dispatch: {}
jobs:
  submit-deps:
    runs-on: ubuntu-latest
    steps:
      - name: Component detection dependency submission
        uses: c-teleport/common-gh-actions/dotnet/security/dependency-submission@v2
        with:
          dotnet-version: 8.0.x
          github-token: ${{ secrets.PACKAGE_TOKEN }}
