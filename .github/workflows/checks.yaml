name: Checks
on:
  push:
    branches:
      - "**"
  pull_request:
    branches:
      - dev
      - master
      - staging
jobs:
  run-test-checks:
    name: Unit Tests
    uses: c-teleport/common-gh-actions/.github/workflows/dotnet-tests-unit-and-coverage.yaml@v2
    with:
      dotnet-version: 8.0.x
    secrets: inherit
  run-integration-test-checks:
    name: Integration Tests
    if: |
      (github.event_name == 'push' && (github.ref == 'refs/heads/master' || github.ref == 'refs/heads/staging' || github.ref == 'refs/heads/dev')) ||
      (github.event_name == 'pull_request' && (github.base_ref == 'master' || github.base_ref == 'staging' || github.base_ref == 'dev'))
    uses: c-teleport/common-gh-actions/.github/workflows/dotnet-tests-integration.yaml@v2
    with:
      dotnet-version: 8.0.x
    secrets: inherit
  run-lint-checks:
    name: <PERSON>t
    uses: c-teleport/common-gh-actions/.github/workflows/dotnet-lint-and-format.yaml@v2
    with:
      enabled: true
    secrets: inherit
  checks-summary:
    runs-on: ubuntu-latest
    needs:
      - run-test-checks
      - run-integration-test-checks
      - run-lint-checks
    if: always()
    steps:
      - name: Check status of all dependent jobs
        run: |-
          echo "Test status: ${{ needs.run-test-checks.result }}"
          echo "Lint status: ${{ needs.run-lint-checks.result }}"
          echo "Integration Test status: ${{ needs.run-integration-test-checks.result }}"

          if [[ "${{ needs.run-test-checks.result }}" == "success" && \
                "${{ needs.run-lint-checks.result }}" != "failure" && \
                "${{ needs.run-integration-test-checks.result }}" != "failure" ]]; then
            echo "All PR checks passed successfully!"
            exit 0
          else
            echo "One or more PR checks failed."
            exit 1
          fi
