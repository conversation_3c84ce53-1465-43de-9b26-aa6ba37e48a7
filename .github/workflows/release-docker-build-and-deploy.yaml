name: "Release: Docker Build and Deploy"
on:
  push:
    branches:
      - dev
      - master
  workflow_dispatch: {}
permissions:
  contents: write
  id-token: write
jobs:
  docker-build-and-deploy:
    name: Docker Build and Deploy
    if: ${{ github.event_name == 'push' || (github.event_name == 'workflow_dispatch' && (github.ref == 'refs/heads/master' || github.ref == 'refs/heads/staging' || github.ref == 'refs/heads/dev')) }}
    uses: c-teleport/common-gh-actions/.github/workflows/docker-build-and-deploy.yaml@v2
    secrets: inherit
    with:
      ecr-repository: c-teleport/booking-cancellation
