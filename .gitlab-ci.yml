include:
  - project: 'c-teleport/dotnet-shared/templates/nuget-ci'
    file: 'package.net8.0.gitlab-ci.yml'

variables:
  SOLUTION_DIRECTORY: "./"

format check:
  stage: .pre
  script:
    - dotnet tool restore
    - dotnet csharpier --check .

run tests:
  image: mcr.microsoft.com/dotnet/sdk:8.0-alpine
  stage: test
  services:
    - name: docker:dind
      command: ["--tls=false"]
  variables:
    DOCKER_HOST: "tcp://docker:2375"
    DOCKER_TLS_CERTDIR: ""
    DOCKER_DRIVER: overlay2
  script:
    - dotnet test --filter "FullyQualifiedName~Integration"
  tags:
    - docker
